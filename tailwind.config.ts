import type { Config } from "tailwindcss";

export default {
    darkMode: ["class"],
    content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
  	extend: {
  		colors: {
  			background: 'hsl(var(--background))',
  			foreground: 'hsl(var(--foreground))',
  			card: {
  				DEFAULT: 'hsl(var(--card))',
  				foreground: 'hsl(var(--card-foreground))'
  			},
  			popover: {
  				DEFAULT: 'hsl(var(--popover))',
  				foreground: 'hsl(var(--popover-foreground))'
  			},
  			primary: {
  				DEFAULT: '#4F46E5',
  				foreground: '#FFFFFF'
  			},
  			secondary: {
  				DEFAULT: '#EC4899',
  				foreground: '#FFFFFF'
  			},
  			muted: {
  				DEFAULT: '#F3F4F6',
  				foreground: '#6B7280'
  			},
  			accent: {
  				DEFAULT: '#9333EA',
  				foreground: '#FFFFFF'
  			},
  			destructive: {
  				DEFAULT: '#EF4444',
  				foreground: '#FFFFFF'
  			},
  			border: '#E5E7EB',
  			input: '#E5E7EB',
  			ring: '#4F46E5',
  			gradient: {
  				primary: 'linear-gradient(90deg, #4F46E5 0%, #9333EA 50%, #EC4899 100%)',
  				secondary: 'linear-gradient(90deg, #EC4899 0%, #9333EA 100%)'
  			},
  			chart: {
  				'1': '#4F46E5',
  				'2': '#9333EA',
  				'3': '#EC4899',
  				'4': '#F472B6',
  				'5': '#F9A8D4'
  			}
  		},
  		borderRadius: {
  			lg: 'var(--radius)',
  			md: 'calc(var(--radius) - 2px)',
  			sm: 'calc(var(--radius) - 4px)'
  		}
  	}
  },
  plugins: [
    require("tailwindcss-animate"),
    require('@tailwindcss/typography'),
    function({ addUtilities }: { addUtilities: (utilities: Record<string, any>) => void }) {
      const newUtilities = {
        '.bg-gradient-primary': {
          background: 'linear-gradient(90deg, #4F46E5 0%, #9333EA 50%, #EC4899 100%)',
        },
        '.bg-gradient-secondary': {
          background: 'linear-gradient(90deg, #EC4899 0%, #9333EA 100%)',
        },
        '.text-gradient-primary': {
          background: 'linear-gradient(90deg, #4F46E5 0%, #9333EA 50%, #EC4899 100%)',
          '-webkit-background-clip': 'text',
          '-webkit-text-fill-color': 'transparent',
        },
        '.glass': {
          background: 'rgba(255, 255, 255, 0.1)',
          backdropFilter: 'blur(10px)',
          border: '1px solid rgba(255, 255, 255, 0.18)',
        }
      }
      addUtilities(newUtilities)
    }
  ],
} satisfies Config;
