import type { CharacterFormData } from '@/components/CharacterForm';
import type { Persona } from '@prisma/client';

export type ChatMessage = {
    id?: string;
    role: 'user' | 'assistant' | 'system';
    content: string;
    createdAt?: Date;
    temporary?: boolean;
    name?: string; // Optional name field for example messages
};

export function replaceVariables(text: string, userName: string, charName: string, defaultPersona?: Persona | null): string {
    const effectiveUserName = defaultPersona?.name || userName;
    return text
        .replace(/{{user}}/g, effectiveUserName)
        .replace(/{{char}}/g, charName)
        .replace(/{{original}}/g, '');
}

export function generateSystemMessages(
    character: CharacterFormData,
    userName: string = 'User',
    defaultPersona?: Persona | null
): ChatMessage[] {
    console.log('Generating system messages for character:', character.name);
    const systemMessages: ChatMessage[] = [];

    // Add base system message for character name
    systemMessages.push({
        role: 'system',
        content: replaceVariables(`Write ${character.name}'s next reply in a fictional chat between ${character.name} and ${userName}.`, userName, character.name, defaultPersona),
        createdAt: new Date()
    });

    // Add persona-based system message if user has a default persona
    if (defaultPersona?.description) {
        systemMessages.push({
            role: 'system',
            content: replaceVariables(defaultPersona.description, userName, character.name, defaultPersona),
            createdAt: new Date()
        });
    }

    // Add character description context if available
    if (character.description) {
        systemMessages.push({
            role: 'system',
            content: replaceVariables(character.description, userName, character.name, defaultPersona),
            createdAt: new Date()
        });
    }

    // Add personality context if available
    if (character.personality) {
        systemMessages.push({
            role: 'system',
            content: replaceVariables(`[${character.name}'s personality: ${character.personality}]`, userName, character.name, defaultPersona),
            createdAt: new Date()
        });
    }

    // Add scenario context if available
    if (character.scenario) {
        systemMessages.push({
            role: 'system',
            content: replaceVariables(`[Circumstances and context of the dialogue: ${character.scenario}]`, userName, character.name, defaultPersona),
            createdAt: new Date()
        });
    }

    // Add dialogue examples if available
    if (character.dialogueExamples) {
        const examples = character.dialogueExamples.split('<START>').filter(Boolean);

        for (const example of examples) {
            // Add example chat marker
            systemMessages.push({
                role: 'system',
                content: '[Example Chat]',
                createdAt: new Date()
            });

            // Split the example into lines and process each line
            const lines = example.trim().split('\n');
            for (const line of lines) {
                if (line.startsWith('{{user}}:')) {
                    systemMessages.push({
                        role: 'system',
                        content: replaceVariables(line.replace('{{user}}:', '').trim(), userName, character.name, defaultPersona),
                        name: 'example_user',
                        createdAt: new Date(),
                    });
                } else if (line.startsWith('{{char}}:')) {
                    systemMessages.push({
                        role: 'system',
                        content: replaceVariables(line.replace('{{char}}:', '').trim(), userName, character.name, defaultPersona),
                        name: 'example_assistant',
                        createdAt: new Date(),
                    });
                } else if (line.trim()) {
                    // Append non-empty lines to the last message
                    const lastMessage = systemMessages[systemMessages.length - 1];
                    if (lastMessage && lastMessage.name) {
                        lastMessage.content += '\n' + replaceVariables(line.trim(), userName, character.name, defaultPersona);
                    }
                }
            }
        }
    }

    // Add start chat marker
    systemMessages.push({
        role: 'system',
        content: '[Start a new Chat]',
        createdAt: new Date()
    });

    // Add first message as a temporary message (not to be saved)
    if (character.firstMessage) {
        systemMessages.push({
            role: 'assistant',
            content: replaceVariables(character.firstMessage, userName, character.name, defaultPersona),
            createdAt: new Date(),
            temporary: true // Mark as temporary
        });
    }

    console.log('Generated system messages:', systemMessages);
    return systemMessages;
} 