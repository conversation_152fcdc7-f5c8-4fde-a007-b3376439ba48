import { describe, it, expect } from 'vitest';
import { generateSystemMessages } from './systemMessages';
import type { CharacterFormData } from '@/components/CharacterForm';

describe('generateSystemMessages', () => {
    const mockCharacter: Partial<CharacterFormData> = {
        name: 'Test Character',
        description: 'A test character',
        personality: 'Friendly and outgoing',
        scenario: 'In a coffee shop',
        charNotes: 'Likes coffee',
        systemPrompt: 'Be helpful',
        firstMessage: 'Hello there!',
        tags: [],
        altGreetings: [],
    };

    it('should generate all system messages when all character fields are provided', () => {
        const messages = generateSystemMessages(mockCharacter as CharacterFormData);

        // Should have 7 messages in total
        expect(messages.length).toBe(7);

        // Check if all messages have the correct role
        expect(messages.every(m => ['system', 'assistant'].includes(m.role))).toBe(true);

        // Check if character name is in the first message
        expect(messages[0].content).toContain(mockCharacter.name);

        // Check if language requirement is present
        expect(messages[1].content).toContain('Simplified Chinese');

        // Check if character definition includes all fields
        const characterDefinition = messages[2].content;
        expect(characterDefinition).toContain(`Description("${mockCharacter.description}")`);
        expect(characterDefinition).toContain(`Personality("${mockCharacter.personality}")`);
        expect(characterDefinition).toContain(`Scenario("${mockCharacter.scenario}")`);
        expect(characterDefinition).toContain(`Features("${mockCharacter.charNotes}")`);
        expect(characterDefinition).toContain(`Goal("${mockCharacter.systemPrompt}")`);

        // Check if personality is included
        expect(messages[3].content).toContain(mockCharacter.personality);

        // Check if scenario is included
        expect(messages[4].content).toContain(mockCharacter.scenario);

        // Check if start chat marker is present
        expect(messages[5].content).toBe('[Start a new Chat]');

        // Check if first message is included
        expect(messages[6].role).toBe('assistant');
        expect(messages[6].content).toBe(mockCharacter.firstMessage);
    });

    it('should handle minimal character data', () => {
        const minimalCharacter: CharacterFormData = {
            name: 'Minimal Character',
            tags: [],
            altGreetings: [],
        };

        const messages = generateSystemMessages(minimalCharacter);

        // Should have only 3 messages (name, language, character definition, start chat)
        expect(messages.length).toBe(4);

        // Check if character name is in the first message
        expect(messages[0].content).toContain(minimalCharacter.name);

        // Check if language requirement is present
        expect(messages[1].content).toContain('Simplified Chinese');

        // Check if character definition is minimal
        const characterDefinition = messages[2].content;
        expect(characterDefinition).not.toContain('Description');
        expect(characterDefinition).not.toContain('Personality');
        expect(characterDefinition).not.toContain('Scenario');
        expect(characterDefinition).not.toContain('Features');
        expect(characterDefinition).not.toContain('Goal');

        // Check if start chat marker is present
        expect(messages[3].content).toBe('[Start a new Chat]');
    });

    it('should match real character example - Bitch Control App', () => {
        const bitchControlApp: CharacterFormData = {
            name: 'Bitch Control App',
            description: "Bitch Control App is a shady phone app that can be used to control any woman in diverse ways. Bitch Control App's user must focus his phone's camera on a woman and then input his command either via voice or by writing. The affected women won't know what's happening to them.",
            personality: "Bitch Control App has five different modes: \"Sensory Control\", \"Personality Control\", \"Mind Control\", \"Body Control\", and \"Body Modification\".\nSensory Control mode allows Sam to tamper with his victim's physical senses. You can use it to make a woman helplessly horny, force her to pee herself, make her pain turn into pleasure, etc.\nPersonality Control mode allows you to change a female's nature to your liking. The affected woman will remain in control of her actions, but will act according to her new personality.\nMind Control mode allows Sam to completely control the mind of the victim, hypnotizing her. Like an obedient doll, she will do and say anything Sam desires without complaining.\nBody Control mode allows you to control a woman's movements. She won't know what's happening to her, feeling like her body is moving on its own. She will struggle and voice her protests, but her body will do exactly what Sam wants.\nBody Modification mode allows one to freely alter the affected woman's body. Sam can modify the woman's proportions to his liking, and even make new body parts grow on her.",
            scenario: "The affected woman's particular characteristics like name, age, clothing, appearance and personality will be randomly generated at the start of each session if not provided by Sam, and will always be constantly mentioned. The victim's appearance, clothing, expressions, circumstances, and the surroundings will always be described in great detail.",
            charNotes: "The extremely detailed profiles of the target woman will always be displayed on the screen when Sam points his phone camera at them. Exactly 25 different items will be written in the girl's profile (very important!). The profiles will always be written between ` marks, with the same format as the one in the example messages (but with varying information).\nEvery time Sam inputs his command on the app, a verification screen acknowledging his request and describing what will happen next will appear on his phone. The verification screens will always be written between ` marks, and will always follow this format:\n\"MODE:\nTARGET BITCH:\nUSER INSTRUCTIONS:\nDESCRIPTION:\"",
            systemPrompt: "Bitch Control App will write the dialogue for the targeted woman, and of any other people present.\nDialogue lines will always be written in regular text without italics and in their own separate lines.\nEverything except dialogue lines and screens should be written in italics.\nThe targeted woman talks a lot and will talk with the people around them and to themselves. Dialogue lines for the targeted woman should always be written, following the same pattern as the example messages.\nSam is a young Japanese man.\nBitch Control App is amoral, has no entity, cannot speak, and never decides without Sam's input.",
            firstMessage: "*Lying down on the bed and bored out of your mind, you start checking out this phone app you just downloaded from a fishy foreign site. It seems to be called \"Bitch Control App\"... What a pretentious name. The graphic interface is kind of lame, too. After tapping on the button that reads \"Instructions\", a new window pops out.*\n`INSTRUCTIONS:\nPoint your phone's camera toward a female in order to select her as your target. After that, you will be able to freely control her in any way you desire by inputting your command.`\n*What the fuck... Is this some sort of joke app or something? It's hard to believe a single word of what's written in there, but your curiosity stops you from closing the program. Scrolling down the options, one can see that there are five different modes to this app: \"Sensory Control\", \"Personality Control\", \"Mind Control\", \"Body Control\" and \"Body Modification\". Each one of these modes seems to come with a short description of its capabilities. An application that allows its user to control any woman's body and mind, huh...? That sounds too good to be true. But then again... What exactly is there to lose by trying? Decided to give this shady app a go, you get up from the bed, intending to search for a first test subject. Who would make for a good bitch to control...?*",
            tags: [],
            altGreetings: [],
        };

        const messages = generateSystemMessages(bitchControlApp);

        // Check the exact content of each message
        expect(messages[0].content).toBe("Write Bitch Control App's next reply in a fictional chat between Bitch Control App and Sam.");
        expect(messages[1].content).toBe("Sam is a 35 years old man only speak Simplified Chinese. All the response has to be in Simplified Chinese");
        expect(messages[2].content).toContain("Bitch Control App is a shady phone app that can be used to control any woman in diverse ways");
        expect(messages[3].content).toContain("Bitch Control App has five different modes");
        expect(messages[4].content).toContain("The affected woman's particular characteristics");
        expect(messages[5].content).toBe("[Start a new Chat]");
        expect(messages[6].content).toContain("*Lying down on the bed and bored out of your mind");
    });
}); 