import type { ChatMessage as ChatMessageType } from '@/lib/systemMessages';
import ReactMarkdown from 'react-markdown';
import { useState } from 'react';

interface ChatMessageProps {
    message: ChatMessageType;
    characterName?: string;
    characterAvatar?: string;
}

const ChatMessage = ({ message, characterName, characterAvatar }: ChatMessageProps) => {
    const [imageError, setImageError] = useState(false);
    const isUser = message.role === 'user';

    // Generate a simple avatar placeholder if no avatar is provided
    const getAvatarPlaceholder = (name: string) => {
        return name ? name.charAt(0).toUpperCase() : 'A';
    };

    return (
        <div className={`flex ${isUser ? 'justify-end animate-slide-in-right' : 'justify-start animate-slide-in-left'} w-full mb-4 group`}>
            {/* AI Avatar - only show for assistant messages */}
            {!isUser && (
                <div className="shrink-0 mr-3">
                    {/* Decorative border around avatar */}
                    <div className="relative">
                        <div className="absolute -inset-0.5 bg-gradient-to-r from-rose-300 via-pink-300 to-purple-300 rounded-full blur-sm opacity-60"></div>
                        <div className="relative w-10 h-10 rounded-full overflow-hidden bg-gradient-to-br from-rose-200 via-pink-200 to-purple-200 flex items-center justify-center shadow-xl border-2 border-white/50">
                            {characterAvatar && !imageError ? (
                                <img
                                    src={characterAvatar}
                                    alt={characterName || 'AI'}
                                    className="w-full h-full object-cover"
                                    onError={() => setImageError(true)}
                                />
                            ) : (
                                <span className="text-rose-700 font-bold text-sm">
                                    {getAvatarPlaceholder(characterName || 'AI')}
                                </span>
                            )}
                        </div>
                    </div>
                </div>
            )}

            {/* Message Container */}
            <div className={`flex flex-col ${isUser ? 'items-end' : 'items-start'} max-w-[75%] sm:max-w-[65%]`}>
                {/* Message Bubble */}
                <div className={`relative px-5 py-4 rounded-3xl shadow-xl backdrop-blur-sm message-bubble border-2 ${isUser
                    ? 'bg-gradient-to-br from-rose-400 via-pink-400 to-purple-400 text-white rounded-br-lg border-rose-300/50'
                    : 'bg-white/90 text-rose-800 rounded-bl-lg border-rose-200/50'
                    }`}>
                    {/* Decorative elements for AI messages */}
                    {!isUser && (
                        <div className="absolute top-2 right-2 text-rose-300/30 text-xs">♡</div>
                    )}
                    {/* Message Content */}
                    <div className={`prose prose-sm max-w-none break-words overflow-hidden ${isUser
                        ? 'prose-p:text-white prose-em:text-rose-100 prose-strong:text-rose-100 prose-h3:text-white prose-code:text-rose-100 prose-quotations:text-rose-100'
                        : 'prose-p:text-rose-800 prose-em:text-rose-700 prose-strong:text-rose-900 prose-h3:text-rose-800 prose-code:text-rose-700 prose-quotations:text-rose-700'
                        } prose-p:my-0 prose-p:leading-relaxed prose-pre:whitespace-pre-wrap prose-pre:break-words prose-code:break-words prose-h3:text-base prose-h3:font-semibold prose-h3:my-1`}>
                        <ReactMarkdown
                            components={{
                                p: ({ children }) => (
                                    <p className="whitespace-pre-line leading-relaxed">
                                        {children}
                                    </p>
                                ),
                                code: ({ children }) => (
                                    <code className={`px-1.5 py-0.5 rounded text-sm ${isUser
                                        ? 'bg-white/20'
                                        : 'bg-rose-100 text-rose-800'
                                        }`}>
                                        {children}
                                    </code>
                                ),
                                pre: ({ children }) => (
                                    <pre className={`p-3 rounded-lg mt-2 overflow-x-auto ${isUser
                                        ? 'bg-white/30'
                                        : 'bg-rose-50 text-rose-800'
                                        }`}>
                                        {children}
                                    </pre>
                                ),
                                em: ({ children }) => <em className="font-normal italic">{children}</em>,
                                h3: ({ children }) => <h3 className="font-semibold my-1">{children}</h3>
                            }}
                        >
                            {message.content.replace(/[""]([^""]+)[""]/g, '**"$1"**')}
                        </ReactMarkdown>
                    </div>
                </div>

                {/* Timestamp */}
                {message.createdAt && !message.temporary && (
                    <div className={`text-xs text-gray-400 mt-1 px-1 ${isUser ? 'text-right' : 'text-left'}`}>
                        {new Date(message.createdAt).toLocaleTimeString([], {
                            hour: '2-digit',
                            minute: '2-digit'
                        })}
                    </div>
                )}
            </div>

            {/* User Avatar - only show for user messages */}
            {isUser && (
                <div className="shrink-0 ml-3">
                    <div className="w-10 h-10 rounded-full bg-linear-to-br from-blue-500 to-indigo-600 flex items-center justify-center shadow-lg">
                        <span className="text-white font-semibold text-sm">U</span>
                    </div>
                </div>
            )}
        </div>
    );
};

export default ChatMessage;
