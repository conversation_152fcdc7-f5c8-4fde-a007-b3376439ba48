import type { ChatMessage as ChatMessageType } from '@/lib/systemMessages';
import ReactMarkdown from 'react-markdown';
import { useState } from 'react';

interface ChatMessageProps {
    message: ChatMessageType;
    characterName?: string;
    characterAvatar?: string;
}

const ChatMessage = ({ message, characterName, characterAvatar }: ChatMessageProps) => {
    const [imageError, setImageError] = useState(false);
    const isUser = message.role === 'user';

    // Generate a simple avatar placeholder if no avatar is provided
    const getAvatarPlaceholder = (name: string) => {
        return name ? name.charAt(0).toUpperCase() : 'A';
    };

    return (
        <div className={`flex ${isUser ? 'justify-end animate-slide-in-right' : 'justify-start animate-slide-in-left'} w-full mb-4 group`}>
            {/* AI Avatar - only show for assistant messages */}
            {!isUser && (
                <div className="shrink-0 mr-3">
                    <div className="w-10 h-10 rounded-full overflow-hidden bg-linear-to-br from-purple-500 to-pink-500 flex items-center justify-center shadow-lg">
                        {characterAvatar && !imageError ? (
                            <img
                                src={characterAvatar}
                                alt={characterName || 'AI'}
                                className="w-full h-full object-cover"
                                onError={() => setImageError(true)}
                            />
                        ) : (
                            <span className="text-white font-semibold text-sm">
                                {getAvatarPlaceholder(characterName || 'AI')}
                            </span>
                        )}
                    </div>
                </div>
            )}

            {/* Message Container */}
            <div className={`flex flex-col ${isUser ? 'items-end' : 'items-start'} max-w-[75%] sm:max-w-[65%]`}>
                {/* Message Bubble */}
                <div className={`relative px-4 py-3 rounded-2xl shadow-lg backdrop-blur-xs message-bubble ${isUser
                    ? 'bg-linear-to-br from-blue-500 to-blue-600 text-white rounded-br-md'
                    : 'bg-linear-to-br from-gray-700 to-gray-800 text-white rounded-bl-md border border-gray-600/30'
                    }`}>
                    {/* Message Content */}
                    <div className={`prose prose-sm max-w-none break-words overflow-hidden ${isUser
                        ? 'prose-p:text-white prose-em:text-blue-100 prose-strong:text-blue-100 prose-h3:text-white prose-code:text-blue-100 prose-quotations:text-blue-100'
                        : 'prose-p:text-gray-100 prose-em:text-gray-300 prose-strong:text-white prose-h3:text-white prose-code:text-gray-200 prose-quotations:text-gray-300'
                        } prose-p:my-0 prose-p:leading-relaxed prose-pre:whitespace-pre-wrap prose-pre:break-words prose-code:break-words prose-h3:text-base prose-h3:font-semibold prose-h3:my-1`}>
                        <ReactMarkdown
                            components={{
                                p: ({ children }) => (
                                    <p className="whitespace-pre-line leading-relaxed">
                                        {children}
                                    </p>
                                ),
                                code: ({ children }) => (
                                    <code className="bg-black/20 px-1.5 py-0.5 rounded text-sm">
                                        {children}
                                    </code>
                                ),
                                pre: ({ children }) => (
                                    <pre className="bg-black/30 p-3 rounded-lg mt-2 overflow-x-auto">
                                        {children}
                                    </pre>
                                ),
                                em: ({ children }) => <em className="font-normal italic">{children}</em>,
                                h3: ({ children }) => <h3 className="font-semibold my-1">{children}</h3>
                            }}
                        >
                            {message.content.replace(/[""]([^""]+)[""]/g, '**"$1"**')}
                        </ReactMarkdown>
                    </div>
                </div>

                {/* Timestamp */}
                {message.createdAt && !message.temporary && (
                    <div className={`text-xs text-gray-400 mt-1 px-1 ${isUser ? 'text-right' : 'text-left'}`}>
                        {new Date(message.createdAt).toLocaleTimeString([], {
                            hour: '2-digit',
                            minute: '2-digit'
                        })}
                    </div>
                )}
            </div>

            {/* User Avatar - only show for user messages */}
            {isUser && (
                <div className="shrink-0 ml-3">
                    <div className="w-10 h-10 rounded-full bg-linear-to-br from-blue-500 to-indigo-600 flex items-center justify-center shadow-lg">
                        <span className="text-white font-semibold text-sm">U</span>
                    </div>
                </div>
            )}
        </div>
    );
};

export default ChatMessage;
