import { useState } from 'react';
import { ArrowLeft, MoreVertical, Phone, Video, Info } from 'lucide-react';
import { useRouter } from 'next/navigation';

interface ChatHeaderProps {
    characterName: string;
    characterAvatar?: string;
    isOnline?: boolean;
    lastSeen?: Date;
    onBack?: () => void;
}

const ChatHeader = ({
    characterName,
    characterAvatar,
    isOnline = true,
    lastSeen,
    onBack
}: ChatHeaderProps) => {
    const [imageError, setImageError] = useState(false);
    const router = useRouter();

    const handleBack = () => {
        if (onBack) {
            onBack();
        } else {
            router.back();
        }
    };

    const getAvatarPlaceholder = (name: string) => {
        return name ? name.charAt(0).toUpperCase() : 'A';
    };

    const getStatusText = () => {
        if (isOnline) {
            return 'Online';
        } else if (lastSeen) {
            const now = new Date();
            const diffMs = now.getTime() - lastSeen.getTime();
            const diffMins = Math.floor(diffMs / (1000 * 60));
            const diffHours = Math.floor(diffMins / 60);
            const diffDays = Math.floor(diffHours / 24);

            if (diffMins < 1) return 'Just now';
            if (diffMins < 60) return `${diffMins}m ago`;
            if (diffHours < 24) return `${diffHours}h ago`;
            return `${diffDays}d ago`;
        }
        return 'Offline';
    };

    return (
        <div className="bg-white/80 backdrop-blur-sm border-b border-rose-200/50 px-4 py-4 shadow-xl relative overflow-hidden">
            {/* Romantic background elements */}
            <div className="absolute inset-0 bg-gradient-to-r from-rose-100/50 via-pink-100/50 to-purple-100/50"></div>
            <div className="absolute top-0 right-0 w-32 h-32 bg-rose-200/20 rounded-full blur-2xl"></div>
            <div className="absolute bottom-0 left-0 w-24 h-24 bg-pink-200/20 rounded-full blur-2xl"></div>

            <div className="relative max-w-4xl mx-auto flex items-center justify-between">
                {/* Left Section */}
                <div className="flex items-center space-x-4">
                    {/* Back Button */}
                    <button
                        onClick={handleBack}
                        className="p-2 text-rose-600 hover:text-rose-700 hover:bg-rose-100/50 rounded-full transition-all duration-200 lg:hidden"
                        title="Go back"
                    >
                        <ArrowLeft size={20} />
                    </button>

                    {/* Character Avatar */}
                    <div className="relative">
                        {/* Decorative border around avatar */}
                        <div className="absolute -inset-1 bg-gradient-to-r from-rose-300 via-pink-300 to-purple-300 rounded-full blur-sm opacity-60"></div>

                        <div className="relative w-14 h-14 rounded-full overflow-hidden bg-gradient-to-br from-rose-200 via-pink-200 to-purple-200 flex items-center justify-center shadow-xl border-2 border-white/50">
                            {characterAvatar && !imageError ? (
                                <img
                                    src={characterAvatar}
                                    alt={characterName}
                                    className="w-full h-full object-cover"
                                    onError={() => setImageError(true)}
                                />
                            ) : (
                                <span className="text-rose-700 font-bold text-xl">
                                    {getAvatarPlaceholder(characterName)}
                                </span>
                            )}
                        </div>

                        {/* Online Status Indicator */}
                        <div className={`absolute -bottom-0.5 -right-0.5 w-5 h-5 rounded-full border-3 border-white shadow-lg ${isOnline ? 'bg-gradient-to-r from-green-400 to-green-500' : 'bg-gray-400'
                            }`}>
                            {isOnline && (
                                <div className="absolute inset-0.5 bg-green-300 rounded-full animate-pulse"></div>
                            )}
                        </div>
                    </div>

                    {/* Character Info */}
                    <div className="flex flex-col">
                        <h1 className="text-xl font-bold bg-gradient-to-r from-rose-700 via-pink-700 to-purple-700 bg-clip-text text-transparent leading-tight">
                            💕 {characterName}
                        </h1>
                        <p className={`text-sm leading-tight font-medium ${isOnline ? 'text-green-600' : 'text-rose-500'
                            }`}>
                            {isOnline && '✨ '}{getStatusText()}
                        </p>
                    </div>
                </div>

                {/* Right Section - Action Buttons */}
                <div className="flex items-center space-x-2">
                    {/* Voice Call Button */}
                    <button
                        className="p-2 text-rose-600 hover:text-rose-700 hover:bg-rose-100/50 rounded-full transition-all duration-200 shadow-md hover:shadow-lg"
                        title="Voice call"
                    >
                        <Phone size={20} />
                    </button>

                    {/* Video Call Button */}
                    <button
                        className="p-2 text-pink-600 hover:text-pink-700 hover:bg-pink-100/50 rounded-full transition-all duration-200 shadow-md hover:shadow-lg"
                        title="Video call"
                    >
                        <Video size={20} />
                    </button>

                    {/* Info Button */}
                    <button
                        className="p-2 text-purple-600 hover:text-purple-700 hover:bg-purple-100/50 rounded-full transition-all duration-200 shadow-md hover:shadow-lg"
                        title="Character info"
                    >
                        <Info size={20} />
                    </button>

                    {/* More Options */}
                    <button
                        className="p-2 text-rose-600 hover:text-rose-700 hover:bg-rose-100/50 rounded-full transition-all duration-200 shadow-md hover:shadow-lg"
                        title="More options"
                    >
                        <MoreVertical size={20} />
                    </button>
                </div>
            </div>
        </div>
    );
};

export default ChatHeader;
