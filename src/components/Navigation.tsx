'use client'

import Link from 'next/link'
import { useState, useEffect } from 'react'
import { Home, Users, History, LogIn, LogOut, Menu, User, Settings } from 'lucide-react'
import { useSession, signOut } from 'next-auth/react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import { useTranslations } from 'use-intl'

export function Navigation() {
  const [isOpen, setIsOpen] = useState(false)
  const { data: session } = useSession()
  const t = useTranslations('navigation')

  // Close sidebar on mobile when route changes
  useEffect(() => {
    const handleRouteChange = () => {
      if (window.innerWidth < 768) {
        setIsOpen(false)
      }
    }
    window.addEventListener('popstate', handleRouteChange)
    return () => window.removeEventListener('popstate', handleRouteChange)
  }, [])

  return (
    <>
      {/* Mobile Header */}
      <div className="md:hidden fixed top-0 left-0 right-0 h-[60px] bg-zinc-950 border-b border-zinc-800 px-4 flex items-center justify-between z-50">
        <span className="text-lg font-semibold text-white">{t('brand')}</span>
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="p-2 hover:bg-zinc-800 rounded-md"
        >
          <Menu className="w-6 h-6" />
        </button>
      </div>

      {/* Backdrop */}
      {isOpen && (
        <div
          className="md:hidden fixed inset-0 bg-black/50 z-40"
          onClick={() => setIsOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div
        className={`
          fixed top-0 left-0 h-screen bg-zinc-950 text-zinc-400 p-4 transition-all duration-300 z-50
          md:w-[300px] md:translate-x-0
          ${isOpen ? 'w-[300px] translate-x-0' : 'w-[300px] -translate-x-full'}
        `}
      >
        <div className="flex flex-col h-full">
          {/* Logo/Header - Only visible on desktop */}
          <div className="hidden md:flex items-center mb-8">
            <span className="text-lg font-semibold text-white">{t('brand')}</span>
          </div>

          {/* Navigation Links */}
          <nav className="flex-1 mt-16 md:mt-0">
            <ul className="space-y-2">
              <li>
                <Link
                  href="/"
                  className="flex items-center p-2 hover:bg-zinc-800 rounded-md group transition-colors"
                >
                  <Home className="w-5 h-5" />
                  <span className="ml-3">{t('home')}</span>
                </Link>
              </li>
              <li>
                <Link
                  href="/characters"
                  className="flex items-center p-2 hover:bg-zinc-800 rounded-md group transition-colors"
                >
                  <Users className="w-5 h-5" />
                  <span className="ml-3">{t('characters')}</span>
                </Link>
              </li>
              <li>
                <Link
                  href="/chat-history"
                  className="flex items-center p-2 hover:bg-zinc-800 rounded-md group transition-colors"
                >
                  <History className="w-5 h-5" />
                  <span className="ml-3">{t('chatHistory')}</span>
                </Link>
              </li>
            </ul>
          </nav>

          {/* User Section */}
          <div className="mt-auto">
            {session?.user ? (
              <DropdownMenu>
                <DropdownMenuTrigger className="flex w-full items-center p-2 hover:bg-zinc-800 rounded-md group transition-colors">
                  <User className="w-5 h-5" />
                  <span className="ml-3 text-zinc-200">{session.user.email}</span>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-[calc(300px-2rem)] bg-zinc-900 text-zinc-200 border-zinc-800">
                  <DropdownMenuItem
                    asChild
                    className="hover:bg-zinc-800 cursor-pointer"
                  >
                    <Link href="/settings">
                      <Settings className="w-5 h-5" />
                      <span>{t('settings')}</span>
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator className="bg-zinc-800" />
                  <DropdownMenuItem
                    onClick={() => signOut()}
                    className="hover:bg-zinc-800 cursor-pointer"
                  >
                    <LogOut className="w-5 h-5" />
                    <span>{t('signOut')}</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <Link
                href="/login"
                className="flex items-center p-2 hover:bg-zinc-800 rounded-md group transition-colors"
              >
                <LogIn className="w-5 h-5" />
                <span className="ml-3">{t('login')}</span>
              </Link>
            )}
          </div>
        </div>
      </div>
    </>
  )
}
