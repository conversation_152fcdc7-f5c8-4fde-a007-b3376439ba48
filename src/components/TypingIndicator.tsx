import { useState } from 'react';

interface TypingIndicatorProps {
    characterName?: string;
    characterAvatar?: string;
    isVisible: boolean;
}

const TypingIndicator = ({ characterName, characterAvatar, isVisible }: TypingIndicatorProps) => {
    const [imageError, setImageError] = useState(false);

    const getAvatarPlaceholder = (name: string) => {
        return name ? name.charAt(0).toUpperCase() : 'A';
    };

    if (!isVisible) return null;

    return (
        <div className="flex justify-start w-full mb-4 animate-fade-in">
            {/* AI Avatar */}
            <div className="shrink-0 mr-3">
                {/* Decorative border around avatar */}
                <div className="relative">
                    <div className="absolute -inset-0.5 bg-gradient-to-r from-rose-300 via-pink-300 to-purple-300 rounded-full blur-sm opacity-60"></div>
                    <div className="relative w-10 h-10 rounded-full overflow-hidden bg-gradient-to-br from-rose-200 via-pink-200 to-purple-200 flex items-center justify-center shadow-xl border-2 border-white/50">
                        {characterAvatar && !imageError ? (
                            <img
                                src={characterAvatar}
                                alt={characterName || 'AI'}
                                className="w-full h-full object-cover"
                                onError={() => setImageError(true)}
                            />
                        ) : (
                            <span className="text-rose-700 font-bold text-sm">
                                {getAvatarPlaceholder(characterName || 'AI')}
                            </span>
                        )}
                    </div>
                </div>
            </div>

            {/* Typing Bubble */}
            <div className="flex flex-col items-start max-w-[75%] sm:max-w-[65%]">
                <div className="bg-white/90 text-rose-800 rounded-3xl rounded-bl-lg border-2 border-rose-200/50 px-5 py-4 shadow-xl backdrop-blur-sm relative">
                    {/* Decorative heart */}
                    <div className="absolute top-2 right-2 text-rose-300/30 text-xs">♡</div>

                    <div className="flex items-center space-x-2">
                        <div className="flex space-x-1">
                            <div
                                className="w-2 h-2 bg-rose-400 rounded-full animate-bounce"
                                style={{ animationDelay: '0ms', animationDuration: '1.4s' }}
                            ></div>
                            <div
                                className="w-2 h-2 bg-pink-400 rounded-full animate-bounce"
                                style={{ animationDelay: '0.2s', animationDuration: '1.4s' }}
                            ></div>
                            <div
                                className="w-2 h-2 bg-purple-400 rounded-full animate-bounce"
                                style={{ animationDelay: '0.4s', animationDuration: '1.4s' }}
                            ></div>
                        </div>
                        <span className="text-xs text-rose-600 ml-2 font-medium">
                            💭 {characterName || 'AI'} is typing...
                        </span>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default TypingIndicator;
