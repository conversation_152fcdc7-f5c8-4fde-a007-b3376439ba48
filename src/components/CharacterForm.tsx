'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { useTranslations } from 'next-intl';

interface CharacterFormProps {
  initialData?: CharacterFormData;
  mode: 'create' | 'edit';
  characterId?: string;
}

export type CharacterFormData = {
  id?: string;
  name: string;
  tags: string[];
  creatorNotes?: string;
  description?: string;
  firstMessage?: string;
  altGreetings: string[];
  personality?: string;
  scenario?: string;
  charNotes?: string;
  charNotesDepth?: number;
  dialogueExamples?: string;
  systemPrompt?: string;
  postInstruction?: string;
};

export function CharacterForm({ initialData, mode, characterId }: CharacterFormProps) {
  const router = useRouter();
  const t = useTranslations('characters');
  const [formData, setFormData] = useState<CharacterFormData>({
    name: initialData?.name || '',
    tags: initialData?.tags || [],
    altGreetings: initialData?.altGreetings || [],
    creatorNotes: initialData?.creatorNotes || '',
    description: initialData?.description || '',
    firstMessage: initialData?.firstMessage || '',
    personality: initialData?.personality || '',
    scenario: initialData?.scenario || '',
    charNotes: initialData?.charNotes || '',
    charNotesDepth: initialData?.charNotesDepth || 2,
    dialogueExamples: initialData?.dialogueExamples || '',
    systemPrompt: initialData?.systemPrompt || '',
    postInstruction: initialData?.postInstruction || '',
  });

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    try {
      const url = mode === 'create'
        ? '/api/characters'
        : `/api/characters/${characterId}`;

      const method = mode === 'create' ? 'POST' : 'PUT';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) throw new Error('Failed to save character');

      const character = await response.json();
      router.push(`/characters/${character.id}`);
    } catch (error) {
      console.error('Error saving character:', error);
    }
  };

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    const isNumberField = typeof formData[name as keyof CharacterFormData] === 'number';
    setFormData(prev => ({
      ...prev,
      [name]: isNumberField ? Number(value) : value,
    }));
  };

  const handleArrayInputChange = (
    name: 'tags' | 'altGreetings',
    value: string
  ) => {
    setFormData(prev => ({
      ...prev,
      [name]: value.split(',').map(item => item.trim()),
    }));
  };

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">
        {mode === 'create' ? 'Create Character' : 'Edit Character'}
      </h1>
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="name" className="block mb-2">Name *</label>
          <input
            id="name"
            name="name"
            type="text"
            value={formData.name}
            onChange={handleInputChange}
            className="w-full p-2 border rounded text-black"
            required
          />
        </div>

        <div>
          <label htmlFor="tags" className="block mb-2">Tags (comma-separated)</label>
          <input
            id="tags"
            name="tags"
            type="text"
            value={formData.tags.join(', ')}
            onChange={(e) => handleArrayInputChange('tags', e.target.value)}
            className="w-full p-2 border rounded text-black"
          />
        </div>

        <div>
          <label htmlFor="creatorNotes" className="block mb-2">Creator Notes</label>
          <textarea
            id="creatorNotes"
            name="creatorNotes"
            value={formData.creatorNotes}
            onChange={handleInputChange}
            className="w-full p-2 border rounded text-black"
            rows={4}
          />
        </div>

        <div>
          <label htmlFor="description" className="block mb-2">Description</label>
          <textarea
            id="description"
            name="description"
            value={formData.description}
            onChange={handleInputChange}
            className="w-full p-2 border rounded text-black"
            rows={4}
          />
        </div>

        <div>
          <label htmlFor="firstMessage" className="block mb-2">First Message</label>
          <textarea
            id="firstMessage"
            name="firstMessage"
            value={formData.firstMessage}
            onChange={handleInputChange}
            className="w-full p-2 border rounded text-black"
            rows={4}
          />
        </div>

        <div>
          <label htmlFor="altGreetings" className="block mb-2">
            Alternative Greetings (comma-separated)
          </label>
          <input
            id="altGreetings"
            name="altGreetings"
            type="text"
            value={formData.altGreetings.join(', ')}
            onChange={(e) => handleArrayInputChange('altGreetings', e.target.value)}
            className="w-full p-2 border rounded text-black"
          />
        </div>

        <div>
          <label htmlFor="personality" className="block mb-2">Personality</label>
          <textarea
            id="personality"
            name="personality"
            value={formData.personality}
            onChange={handleInputChange}
            className="w-full p-2 border rounded text-black"
            rows={4}
          />
        </div>

        <div>
          <label htmlFor="scenario" className="block mb-2">Scenario</label>
          <textarea
            id="scenario"
            name="scenario"
            value={formData.scenario}
            onChange={handleInputChange}
            className="w-full p-2 border rounded text-black"
            rows={4}
          />
        </div>

        <div>
          <label htmlFor="charNotes" className="block mb-2">Character Notes</label>
          <textarea
            id="charNotes"
            name="charNotes"
            value={formData.charNotes}
            onChange={handleInputChange}
            className="w-full p-2 border rounded text-black"
            rows={4}
          />
        </div>

        <div>
          <label htmlFor="charNotesDepth" className="block mb-2">Character Notes Depth</label>
          <input
            id="charNotesDepth"
            name="charNotesDepth"
            type="number"
            value={formData.charNotesDepth}
            onChange={handleInputChange}
            className="w-full p-2 border rounded text-black"
          />
        </div>

        <div>
          <label htmlFor="dialogueExamples" className="block mb-2">Dialogue Examples</label>
          <textarea
            id="dialogueExamples"
            name="dialogueExamples"
            value={formData.dialogueExamples}
            onChange={handleInputChange}
            className="w-full p-2 border rounded text-black"
            rows={4}
          />
        </div>

        <div>
          <label htmlFor="systemPrompt" className="block mb-2">System Prompt</label>
          <textarea
            id="systemPrompt"
            name="systemPrompt"
            value={formData.systemPrompt}
            onChange={handleInputChange}
            className="w-full p-2 border rounded text-black"
            rows={4}
          />
        </div>

        <div>
          <label htmlFor="postInstruction" className="block mb-2">Post Instruction</label>
          <textarea
            id="postInstruction"
            name="postInstruction"
            value={formData.postInstruction}
            onChange={handleInputChange}
            className="w-full p-2 border rounded text-black"
            rows={4}
          />
        </div>

        <div className="flex gap-2">
          <Button type="submit">
            {mode === 'create' ? 'Create Character' : 'Save Changes'}
          </Button>
          <Button
            type="button"
            variant="outline"
            onClick={() => router.back()}
          >
            Cancel
          </Button>
        </div>
      </form>
    </div>
  );
} 