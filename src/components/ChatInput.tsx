import { useState, useRef, useEffect } from 'react';
import { Send, Paperclip, Mic, MicOff } from 'lucide-react';

interface ChatInputProps {
    value: string;
    onChange: (value: string) => void;
    onSend: () => void;
    disabled?: boolean;
    placeholder?: string;
}

const ChatInput = ({
    value,
    onChange,
    onSend,
    disabled = false,
    placeholder = "Type a message..."
}: ChatInputProps) => {
    const [isRecording, setIsRecording] = useState(false);
    const textareaRef = useRef<HTMLTextAreaElement>(null);

    // Auto-resize textarea
    useEffect(() => {
        const textarea = textareaRef.current;
        if (textarea) {
            textarea.style.height = 'auto';
            textarea.style.height = `${Math.min(textarea.scrollHeight, 120)}px`;
        }
    }, [value]);

    const handleKeyDown = (e: React.KeyboardEvent) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            if (!disabled && value.trim()) {
                onSend();
            }
        }
    };

    const handleSend = () => {
        if (!disabled && value.trim()) {
            onSend();
        }
    };

    const toggleRecording = () => {
        setIsRecording(!isRecording);
        // TODO: Implement voice recording functionality
    };

    return (
        <div className="bg-white/80 backdrop-blur-sm border-t border-rose-200/50 p-4 relative overflow-hidden">
            {/* Romantic background elements */}
            <div className="absolute inset-0 bg-gradient-to-r from-rose-100/30 via-pink-100/30 to-purple-100/30"></div>
            <div className="absolute top-0 right-0 w-24 h-24 bg-rose-200/20 rounded-full blur-2xl"></div>
            <div className="absolute bottom-0 left-0 w-32 h-32 bg-pink-200/20 rounded-full blur-2xl"></div>

            <div className="relative max-w-4xl mx-auto">
                {/* Input Container */}
                <div className="relative flex items-end gap-3 bg-white/90 backdrop-blur-sm rounded-3xl border-2 border-rose-200/50 p-4 shadow-xl">
                    {/* Decorative elements */}
                    <div className="absolute top-2 right-2 text-rose-300/20 text-xs">♡</div>
                    {/* Attachment Button */}
                    <button
                        type="button"
                        className="shrink-0 p-2 text-rose-600 hover:text-rose-700 hover:bg-rose-100/50 rounded-full transition-all duration-200 disabled:opacity-50 shadow-md hover:shadow-lg"
                        disabled={disabled}
                        title="Attach file"
                    >
                        <Paperclip size={20} />
                    </button>

                    {/* Text Input */}
                    <div className="flex-1 relative">
                        <textarea
                            ref={textareaRef}
                            value={value}
                            onChange={(e) => onChange(e.target.value)}
                            onKeyDown={handleKeyDown}
                            placeholder={placeholder}
                            disabled={disabled}
                            rows={1}
                            className="w-full bg-transparent text-rose-800 placeholder-rose-400 resize-none border-none outline-hidden focus:ring-0 py-2 px-0 text-base leading-relaxed max-h-[120px] overflow-y-auto scrollbar-thin scrollbar-thumb-rose-300 scrollbar-track-transparent"
                            style={{ minHeight: '24px' }}
                        />
                    </div>

                    {/* Voice Recording Button */}
                    <button
                        type="button"
                        onClick={toggleRecording}
                        className={`shrink-0 p-2 rounded-full transition-all duration-200 disabled:opacity-50 shadow-md hover:shadow-lg ${isRecording
                            ? 'text-red-500 bg-red-100/50 hover:bg-red-100/70'
                            : 'text-pink-600 hover:text-pink-700 hover:bg-pink-100/50'
                            }`}
                        disabled={disabled}
                        title={isRecording ? "Stop recording" : "Start voice recording"}
                    >
                        {isRecording ? <MicOff size={20} /> : <Mic size={20} />}
                    </button>

                    {/* Send Button */}
                    <button
                        type="button"
                        onClick={handleSend}
                        disabled={disabled || !value.trim()}
                        className={`shrink-0 p-3 rounded-full transition-all duration-300 shadow-lg hover:shadow-xl ${disabled || !value.trim()
                            ? 'text-rose-400 bg-rose-100/50 cursor-not-allowed'
                            : 'text-white bg-gradient-to-r from-rose-500 via-pink-500 to-purple-500 hover:from-rose-600 hover:via-pink-600 hover:to-purple-600 transform hover:scale-105'
                            }`}
                        title="Send message"
                    >
                        <Send size={20} />
                    </button>
                </div>

                {/* Status Indicators */}
                {isRecording && (
                    <div className="flex items-center justify-center mt-3 text-red-500 text-sm font-medium">
                        <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse mr-2"></div>
                        🎤 Recording...
                    </div>
                )}

                {disabled && (
                    <div className="flex items-center justify-center mt-3 text-rose-600 text-sm font-medium">
                        <div className="flex space-x-1">
                            <div className="w-2 h-2 bg-rose-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
                            <div className="w-2 h-2 bg-pink-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
                            <div className="w-2 h-2 bg-purple-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
                        </div>
                        <span className="ml-2">💭 AI is thinking...</span>
                    </div>
                )}
            </div>
        </div>
    );
};

export default ChatInput;
