import { useState, useRef, useEffect } from 'react';
import { Send, Paperclip, Mic, MicOff } from 'lucide-react';

interface ChatInputProps {
    value: string;
    onChange: (value: string) => void;
    onSend: () => void;
    disabled?: boolean;
    placeholder?: string;
}

const ChatInput = ({ 
    value, 
    onChange, 
    onSend, 
    disabled = false, 
    placeholder = "Type a message..." 
}: ChatInputProps) => {
    const [isRecording, setIsRecording] = useState(false);
    const textareaRef = useRef<HTMLTextAreaElement>(null);

    // Auto-resize textarea
    useEffect(() => {
        const textarea = textareaRef.current;
        if (textarea) {
            textarea.style.height = 'auto';
            textarea.style.height = `${Math.min(textarea.scrollHeight, 120)}px`;
        }
    }, [value]);

    const handleKeyDown = (e: React.KeyboardEvent) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            if (!disabled && value.trim()) {
                onSend();
            }
        }
    };

    const handleSend = () => {
        if (!disabled && value.trim()) {
            onSend();
        }
    };

    const toggleRecording = () => {
        setIsRecording(!isRecording);
        // TODO: Implement voice recording functionality
    };

    return (
        <div className="bg-gray-900/95 backdrop-blur-xs border-t border-gray-700/50 p-4">
            <div className="max-w-4xl mx-auto">
                {/* Input Container */}
                <div className="relative flex items-end gap-3 bg-gray-800/80 backdrop-blur-xs rounded-2xl border border-gray-600/30 p-3 shadow-lg">
                    {/* Attachment Button */}
                    <button
                        type="button"
                        className="shrink-0 p-2 text-gray-400 hover:text-gray-300 hover:bg-gray-700/50 rounded-full transition-all duration-200 disabled:opacity-50"
                        disabled={disabled}
                        title="Attach file"
                    >
                        <Paperclip size={20} />
                    </button>

                    {/* Text Input */}
                    <div className="flex-1 relative">
                        <textarea
                            ref={textareaRef}
                            value={value}
                            onChange={(e) => onChange(e.target.value)}
                            onKeyDown={handleKeyDown}
                            placeholder={placeholder}
                            disabled={disabled}
                            rows={1}
                            className="w-full bg-transparent text-gray-100 placeholder-gray-400 resize-none border-none outline-hidden focus:ring-0 py-2 px-0 text-base leading-relaxed max-h-[120px] overflow-y-auto scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-transparent"
                            style={{ minHeight: '24px' }}
                        />
                    </div>

                    {/* Voice Recording Button */}
                    <button
                        type="button"
                        onClick={toggleRecording}
                        className={`shrink-0 p-2 rounded-full transition-all duration-200 disabled:opacity-50 ${
                            isRecording 
                                ? 'text-red-400 bg-red-500/20 hover:bg-red-500/30' 
                                : 'text-gray-400 hover:text-gray-300 hover:bg-gray-700/50'
                        }`}
                        disabled={disabled}
                        title={isRecording ? "Stop recording" : "Start voice recording"}
                    >
                        {isRecording ? <MicOff size={20} /> : <Mic size={20} />}
                    </button>

                    {/* Send Button */}
                    <button
                        type="button"
                        onClick={handleSend}
                        disabled={disabled || !value.trim()}
                        className={`shrink-0 p-2 rounded-full transition-all duration-200 ${
                            disabled || !value.trim()
                                ? 'text-gray-500 bg-gray-700/50 cursor-not-allowed'
                                : 'text-white bg-linear-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 shadow-lg hover:shadow-blue-500/25 transform hover:scale-105'
                        }`}
                        title="Send message"
                    >
                        <Send size={20} />
                    </button>
                </div>

                {/* Status Indicators */}
                {isRecording && (
                    <div className="flex items-center justify-center mt-2 text-red-400 text-sm">
                        <div className="w-2 h-2 bg-red-400 rounded-full animate-pulse mr-2"></div>
                        Recording...
                    </div>
                )}

                {disabled && (
                    <div className="flex items-center justify-center mt-2 text-gray-400 text-sm">
                        <div className="flex space-x-1">
                            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
                            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
                            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
                        </div>
                        <span className="ml-2">AI is thinking...</span>
                    </div>
                )}
            </div>
        </div>
    );
};

export default ChatInput;
