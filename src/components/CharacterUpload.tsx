'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';

export function CharacterUpload() {
  const router = useRouter();
  const [isUploading, setIsUploading] = useState(false);

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsUploading(true);
    try {
      const jsonContent = await file.text();
      const characterData = JSON.parse(jsonContent);

      // Convert the JSON format to our Character model format
      const character = {
        name: characterData.data.name,
        tags: characterData.data.tags || [],
        creatorNotes: characterData.data.creator_notes,
        description: characterData.data.description,
        firstMessage: characterData.data.first_mes,
        altGreetings: characterData.data.alternate_greetings || [],
        personality: characterData.data.personality,
        scenario: characterData.data.scenario,
        charNotes: characterData.data.extensions?.depth_prompt?.prompt || '', // Using depth_prompt.prompt as charNotes
        charNotesDepth: characterData.data.extensions?.depth_prompt?.depth || 2, // Using depth_prompt.depth as charNotesDepth
        dialogueExamples: characterData.data.mes_example,
        systemPrompt: characterData.data.system_prompt,
        postInstruction: characterData.data.post_history_instructions,
      };

      const response = await fetch('/api/characters', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(character),
      });

      if (!response.ok) throw new Error('Failed to create character');

      // Get the new character data from response
      const newCharacter = await response.json();

      // Navigate to the new character's page
      router.push(`/characters/${newCharacter.id}`);
    } catch (error) {
      console.error('Error uploading character:', error);
      alert('Failed to upload character');
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <Button
      variant="outline"
      disabled={isUploading}
      onClick={() => document.getElementById('character-upload')?.click()}
    >
      <input
        type="file"
        accept=".json"
        onChange={handleFileUpload}
        className="hidden"
        id="character-upload"
      />
      {isUploading ? 'Uploading...' : 'Upload'}
    </Button>
  );
} 