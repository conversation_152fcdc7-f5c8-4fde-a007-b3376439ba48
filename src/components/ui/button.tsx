import { cn } from "@/lib/utils"
import React from "react"

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
    variant?: 'default' | 'outline' | 'outline-solid' | 'destructive'
}

export function Button({
    className,
    variant = 'default',
    ...props
}: ButtonProps) {
    return (
        <button
            className={cn(
                "inline-flex items-center justify-center rounded-lg px-4 py-2 text-sm font-medium transition-colors",
                variant === 'default' && "bg-primary text-primary-foreground hover:bg-primary/90",
                variant === 'outline' && "border border-input bg-background hover:bg-accent hover:text-accent-foreground",
                variant === 'outline-solid' && "border border-input bg-background hover:bg-accent hover:text-accent-foreground",
                variant === 'destructive' && "bg-destructive text-destructive-foreground hover:bg-destructive/90",
                className
            )}
            {...props}
        />
    )
}
