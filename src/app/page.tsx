import Link from "next/link";
import { useTranslations } from 'next-intl';

export default function Home() {
  const t = useTranslations('welcome');

  return (
    <div className="p-4 md:p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-2xl md:text-3xl font-bold mb-3 md:mb-4">{t('welcome')}</h1>
        <p className="text-zinc-400 mb-6 md:mb-8 text-sm md:text-base">
          {t('subtitle')}
        </p>

        <div className="grid gap-4 md:gap-6 md:grid-cols-2">
          <div className="p-4 md:p-6 bg-zinc-900 rounded-lg border border-zinc-800">
            <h2 className="text-lg md:text-xl font-semibold mb-2">{t('characters.title')}</h2>
            <p className="text-zinc-400 mb-4 text-sm md:text-base">
              {t('characters.description')}
            </p>
            <Link
              href="/characters"
              className="inline-flex items-center text-sm text-zinc-50 hover:text-zinc-200"
            >
              {t('characters.browse')} →
            </Link>
          </div>

          <div className="p-4 md:p-6 bg-zinc-900 rounded-lg border border-zinc-800">
            <h2 className="text-lg md:text-xl font-semibold mb-2">{t('history.title')}</h2>
            <p className="text-zinc-400 mb-4 text-sm md:text-base">
              {t('history.description')}
            </p>
            <Link
              href="/chat-history"
              className="inline-flex items-center text-sm text-zinc-50 hover:text-zinc-200"
            >
              {t('history.view')} →
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
