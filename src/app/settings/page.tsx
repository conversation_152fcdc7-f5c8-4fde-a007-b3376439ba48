'use client'

import { useState, useEffect } from 'react'
import { Plus } from 'lucide-react'
import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON>ontent,
    Di<PERSON>Header,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    DialogTrigger,
    DialogFooter,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { useSession } from "next-auth/react"
import { Ta<PERSON>, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs"

interface Persona {
    id: string;
    name: string;
    description: string | null;
    isDefault?: boolean;
}

// Update AllowedLocale type to include 'any'
type AllowedLocale = 'en' | 'zh-CN' | 'zh-TW' | 'any'

export default function SettingsPage() {
    const { data: session } = useSession()
    const [personas, setPersonas] = useState<Persona[]>([
        // Default persona with empty values
        { id: 'default', name: '', description: null },
    ])
    const [editingPersona, setEditingPersona] = useState<Persona | null>(null)
    const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
    const [webLocale, setWebLocale] = useState<AllowedLocale>('en')
    const [chatLocale, setChatLocale] = useState<AllowedLocale>('en')
    const [showSuccess, setShowSuccess] = useState(false)
    const [successMessage, setSuccessMessage] = useState('')

    const loadPersonas = async () => {
        if (session?.user) {
            try {
                const response = await fetch('/api/personas')
                if (!response.ok) {
                    throw new Error('Failed to load personas')
                }
                const data = await response.json()
                // If no personas exist, use the default one
                if (data.length === 0) {
                    setPersonas([{ id: 'default', name: session.user.name || '', description: null }])
                } else {
                    setPersonas(data)
                }
            } catch (error) {
                console.error('Error loading personas:', error)
                // On error, fall back to default persona
                setPersonas([{ id: 'default', name: session.user.name || '', description: null }])
            }
        }
    }

    useEffect(() => {
        loadPersonas()
    }, [session])

    useEffect(() => {
        const loadUserSettings = async () => {
            if (session?.user) {
                try {
                    const response = await fetch('/api/user/settings')
                    if (response.ok) {
                        const settings = await response.json()
                        setWebLocale(settings.webLocale as AllowedLocale || 'en')
                        // Handle null value by converting to 'any'
                        setChatLocale(settings.chatLocale === null ? 'any' : (settings.chatLocale as AllowedLocale || 'en'))
                    }
                } catch (error) {
                    console.error('Error loading settings:', error)
                }
            }
        }
        loadUserSettings()
    }, [session])

    const handleEditPersona = (persona: Persona) => {
        setEditingPersona(persona)
        setIsEditDialogOpen(true)
    }

    const handleAddPersona = () => {
        setEditingPersona({ id: '', name: '', description: null })
        setIsEditDialogOpen(true)
    }

    const handleSavePersona = async () => {
        if (!editingPersona) return

        try {
            const response = await fetch(`/api/personas`, {
                method: editingPersona.id ? 'PUT' : 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(editingPersona),
            })

            if (!response.ok) {
                throw new Error('Failed to save persona')
            }

            await loadPersonas() // Refresh the list after saving
            setIsEditDialogOpen(false)
            setEditingPersona(null)
        } catch (error) {
            console.error('Error saving persona:', error)
            // You might want to show an error message to the user here
        }
    }

    const handleSetDefault = async (persona: Persona) => {
        if (persona.id === 'default') return

        try {
            const response = await fetch(`/api/personas/${persona.id}/default`, {
                method: 'PUT'
            })

            if (!response.ok) {
                throw new Error('Failed to set default persona')
            }

            await loadPersonas() // Refresh the list to update default status
        } catch (error) {
            console.error('Error setting default persona:', error)
            // You might want to show an error message to the user here
        }
    }

    const handleDeletePersona = async () => {
        if (!editingPersona) return

        try {
            // Don't make API call for default persona
            if (editingPersona.id === 'default') {
                setIsEditDialogOpen(false)
                setEditingPersona(null)
                return
            }

            const response = await fetch(`/api/personas/${editingPersona.id}`, {
                method: 'DELETE'
            })

            if (!response.ok) {
                throw new Error('Failed to delete persona')
            }

            await loadPersonas() // Refresh the list after deleting
            setIsEditDialogOpen(false)
            setEditingPersona(null)
        } catch (error) {
            console.error('Error deleting persona:', error)
            // You might want to show an error message to the user here
        }
    }

    const saveLocaleSettings = async () => {
        try {
            const response = await fetch('/api/user/settings', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                // Convert 'any' to null for the API
                body: JSON.stringify({
                    webLocale,
                    chatLocale: chatLocale === 'any' ? null : chatLocale
                }),
            })

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.message || 'Failed to save settings');
            }

            // Show success message
            setSuccessMessage('Language preferences saved successfully!')
            setShowSuccess(true)
            setTimeout(() => setShowSuccess(false), 3000)
        } catch (error) {
            console.error('Error saving locale settings:', error)
            setSuccessMessage(error instanceof Error ? error.message : 'Failed to save preferences')
            setShowSuccess(true)
            setTimeout(() => setShowSuccess(false), 3000)
        }
    }

    return (
        <div className="container mx-auto p-6 max-w-4xl">
            <h1 className="text-2xl font-bold mb-8">Settings</h1>

            <Tabs defaultValue="personas" className="bg-zinc-900 rounded-lg p-6">
                <TabsList className="grid w-full grid-cols-2 mb-6">
                    <TabsTrigger value="personas">Personas</TabsTrigger>
                    <TabsTrigger value="localization">Localization</TabsTrigger>
                </TabsList>

                <TabsContent value="personas">
                    <div className="bg-zinc-900 rounded-lg p-6 mb-8">
                        <div className="flex justify-between items-center mb-6">
                            <h2 className="text-xl font-semibold">Personas</h2>
                            <Button
                                variant="outline"
                                className="flex items-center gap-2 py-1.5"
                                onClick={handleAddPersona}
                            >
                                <Plus className="w-4 h-4" />
                                Add Persona
                            </Button>
                        </div>

                        {/* Personas List */}
                        <div className="space-y-4">
                            {personas.map((persona) => (
                                <div
                                    key={persona.id}
                                    className="flex items-center justify-between p-4 rounded-md bg-zinc-800"
                                >
                                    <div>
                                        <div className="flex items-center gap-2">
                                            <h3 className="font-medium">{persona.name || 'Unnamed'}</h3>
                                            {persona.isDefault && (
                                                <span className="px-2 py-0.5 text-xs bg-zinc-700 text-zinc-300 rounded-full">
                                                    Default
                                                </span>
                                            )}
                                        </div>
                                        <p className="text-sm text-zinc-400">
                                            {persona.description || 'No description'}
                                        </p>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        {!persona.isDefault && persona.id !== 'default' && (
                                            <Button
                                                variant="outline"
                                                className="py-1.5"
                                                onClick={() => handleSetDefault(persona)}
                                            >
                                                Set as Default
                                            </Button>
                                        )}
                                        <Button
                                            variant="outline"
                                            className="py-1.5"
                                            onClick={() => handleEditPersona(persona)}
                                        >
                                            Edit
                                        </Button>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>

                    {/* Edit/Add Persona Dialog */}
                    <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
                        <DialogContent className="bg-zinc-900 text-zinc-200 border-zinc-800">
                            <DialogHeader>
                                <DialogTitle>
                                    {editingPersona?.id ? 'Edit Persona' : 'Add New Persona'}
                                </DialogTitle>
                            </DialogHeader>
                            <div className="space-y-4">
                                <div className="space-y-2">
                                    <label htmlFor="name" className="text-sm font-medium">Name</label>
                                    <Input
                                        id="name"
                                        value={editingPersona?.name || ''}
                                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => setEditingPersona(editingPersona ? {
                                            ...editingPersona,
                                            name: e.target.value
                                        } : null)}
                                        className="bg-zinc-800 border-zinc-700"
                                    />
                                </div>
                                <div className="space-y-2">
                                    <label htmlFor="description" className="text-sm font-medium">Description</label>
                                    <Textarea
                                        id="description"
                                        value={editingPersona?.description || ''}
                                        onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setEditingPersona(editingPersona ? {
                                            ...editingPersona,
                                            description: e.target.value
                                        } : null)}
                                        className="bg-zinc-800 border-zinc-700"
                                    />
                                </div>
                            </div>
                            <DialogFooter className="flex items-center justify-between">
                                {editingPersona?.id && editingPersona.id !== 'default' && (
                                    <Button
                                        variant="destructive"
                                        onClick={handleDeletePersona}
                                        disabled={editingPersona?.id === 'default'}
                                    >
                                        Delete
                                    </Button>
                                )}
                                <div className="flex gap-2">
                                    <Button
                                        variant="outline"
                                        onClick={() => setIsEditDialogOpen(false)}
                                    >
                                        Cancel
                                    </Button>
                                    <Button onClick={handleSavePersona}>
                                        {editingPersona?.id ? 'Save Changes' : 'Create Persona'}
                                    </Button>
                                </div>
                            </DialogFooter>
                        </DialogContent>
                    </Dialog>

                </TabsContent>

                <TabsContent value="localization">
                    <div className="bg-zinc-900 rounded-lg p-6 mb-8">
                        <h2 className="text-xl font-semibold mb-6">Language Preferences</h2>

                        <div className="space-y-6">
                            <div className="space-y-3">
                                <label className="text-sm font-medium">Website Language</label>
                                <select
                                    value={webLocale}
                                    onChange={(e) => setWebLocale(e.target.value as AllowedLocale)}
                                    className="w-full p-2 bg-zinc-800 border border-zinc-700 rounded-md text-zinc-200"
                                >
                                    <option value="en">English</option>
                                    <option value="zh-CN">简体中文 (Chinese Simplified)</option>
                                    <option value="zh-TW">繁體中文 (Chinese Traditional)</option>
                                </select>
                            </div>

                            <div className="space-y-3">
                                <label className="text-sm font-medium">Chat Language</label>
                                <select
                                    value={chatLocale}
                                    onChange={(e) => setChatLocale(e.target.value as AllowedLocale)}
                                    className="w-full p-2 bg-zinc-800 border border-zinc-700 rounded-md text-zinc-200"
                                >
                                    <option value="en">English</option>
                                    <option value="zh-CN">简体中文 (Chinese Simplified)</option>
                                    <option value="zh-TW">繁體中文 (Chinese Traditional)</option>
                                    <option value="any">Any Language</option>
                                </select>
                            </div>

                            <Button
                                onClick={saveLocaleSettings}
                                className="mt-4"
                            >
                                Save Language Preferences
                            </Button>
                            {showSuccess && (
                                <div className={`mt-4 p-3 border rounded-md ${successMessage.includes('Failed')
                                        ? 'bg-red-900/30 border-red-800 text-red-300'
                                        : 'bg-green-900/30 border-green-800 text-green-300'
                                    }`}>
                                    {successMessage}
                                </div>
                            )}
                        </div>
                    </div>
                </TabsContent>
            </Tabs>
        </div>
    )
}
