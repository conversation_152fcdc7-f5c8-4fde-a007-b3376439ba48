import { NextResponse } from 'next/server'
import { auth } from '@/auth'
import { prisma } from '@/lib/prisma'

export async function GET() {
    const session = await auth()
    if (!session?.user) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
        const user = await prisma.user.findUnique({
            where: { id: session.user.id },
            include: {
                personas: {
                    orderBy: { createdAt: 'desc' }
                },
                defaultPersona: true
            }
        })

        if (!user) {
            return NextResponse.json({ error: 'User not found' }, { status: 404 })
        }

        // Add isDefault flag to each persona
        const personas = user.personas.map(persona => ({
            ...persona,
            isDefault: persona.id === user.defaultPersonaId
        }))

        return NextResponse.json(personas)
    } catch (error) {
        console.error('Error fetching personas:', error)
        return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
    }
}

export async function POST(request: Request) {
    const session = await auth()
    if (!session?.user) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
        const { name, description } = await request.json()

        // Create the new persona
        const persona = await prisma.persona.create({
            data: {
                name,
                description,
                userId: session.user.id
            }
        })

        // If this is the only persona, set it as default
        const personaCount = await prisma.persona.count({
            where: { userId: session.user.id }
        })

        if (personaCount === 1) {
            await prisma.user.update({
                where: { id: session.user.id },
                data: { defaultPersonaId: persona.id }
            })
        }

        return NextResponse.json({ ...persona, isDefault: personaCount === 1 })
    } catch (error) {
        console.error('Error creating persona:', error)
        return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
    }
}

export async function PUT(request: Request) {
    const session = await auth()
    if (!session?.user) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
        const { id, name, description } = await request.json()

        // If it's the default persona, create a new one instead of updating
        if (id === 'default') {
            const persona = await prisma.persona.create({
                data: {
                    name,
                    description,
                    userId: session.user.id
                }
            })

            // If this is the only persona, set it as default
            const personaCount = await prisma.persona.count({
                where: { userId: session.user.id }
            })

            if (personaCount === 1) {
                await prisma.user.update({
                    where: { id: session.user.id },
                    data: { defaultPersonaId: persona.id }
                })
            }

            return NextResponse.json({ ...persona, isDefault: personaCount === 1 })
        }

        // Otherwise, update the existing persona
        const persona = await prisma.persona.update({
            where: {
                id,
                userId: session.user.id
            },
            data: {
                name,
                description
            }
        })

        const user = await prisma.user.findUnique({
            where: { id: session.user.id }
        })

        return NextResponse.json({ ...persona, isDefault: persona.id === user?.defaultPersonaId })
    } catch (error) {
        console.error('Error updating persona:', error)
        return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
    }
} 
