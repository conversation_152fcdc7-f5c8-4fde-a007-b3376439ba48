import { NextResponse } from 'next/server'
import { auth } from '@/auth'
import { prisma } from '@/lib/prisma'

export async function PUT(
    request: Request,
    context: { params: { id: string } }
) {
    const session = await auth()
    const { id } = await context.params

    if (!session?.user) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
        const user = await prisma.user.update({
            where: { id: session.user.id },
            data: { defaultPersonaId: id },
            include: { defaultPersona: true }
        })

        if (!user.defaultPersona) {
            return NextResponse.json({ error: 'Failed to set default persona' }, { status: 400 })
        }

        return NextResponse.json(user.defaultPersona)
    } catch (error) {
        console.error('Error setting default persona:', error)
        return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
    }
} 