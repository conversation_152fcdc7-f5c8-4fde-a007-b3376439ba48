import { NextResponse } from 'next/server'
import { auth } from '@/auth'
import { prisma } from '@/lib/prisma'

export async function DELETE(
    request: Request,
    context: { params: { id: string } }
) {
    const session = await auth()
    const { id } = await context.params

    if (!session?.user) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
        // Don't allow deleting the default persona
        if (id === 'default') {
            return NextResponse.json({ error: 'Cannot delete default persona' }, { status: 400 })
        }

        // Start a transaction to handle both deletion and default persona update
        await prisma.$transaction(async (tx) => {
            // Check if this is the default persona and get all personas
            const user = await tx.user.findUnique({
                where: { id: session.user.id },
                include: {
                    personas: {
                        orderBy: { createdAt: 'desc' }
                    }
                }
            })

            if (!user) {
                throw new Error('User not found')
            }

            const isDefaultPersona = id === user.defaultPersonaId
            const remainingPersonas = user.personas.filter(p => p.id !== id)

            // Delete the persona
            await tx.persona.delete({
                where: {
                    id,
                    userId: session.user.id
                }
            })

            // If we deleted the default persona and there are other personas remaining,
            // set the most recently created one as default
            if (isDefaultPersona && remainingPersonas.length > 0) {
                const newDefault = remainingPersonas[0] // Most recent due to orderBy in the query
                await tx.user.update({
                    where: { id: session.user.id },
                    data: { defaultPersonaId: newDefault.id }
                })
            }
        })

        return NextResponse.json({ success: true }, { status: 200 })
    } catch (error) {
        console.error('Error deleting persona:', error)
        return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
    }
} 