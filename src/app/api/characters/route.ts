import { prisma } from '@/lib/prisma';
import { NextResponse } from 'next/server';

export async function GET() {
  try {
    const characters = await prisma.character.findMany({
      orderBy: {
        createdAt: 'desc'
      }
    });

    return NextResponse.json(characters);
  } catch (error) {
    console.error('Error fetching characters:', error);
    return NextResponse.json(
      { error: 'Failed to fetch characters' },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const data = await request.json();
    const character = await prisma.character.create({
      data: {
        name: data.name,
        tags: data.tags,
        creatorNotes: data.creatorNotes,
        description: data.description,
        firstMessage: data.firstMessage,
        altGreetings: data.altGreetings,
        personality: data.personality,
        scenario: data.scenario,
        charNotes: data.charNotes,
        charNotesDepth: data.charNotesDepth,
        dialogueExamples: data.dialogueExamples,
        systemPrompt: data.systemPrompt,
        postInstruction: data.postInstruction,
      }
    });

    return NextResponse.json(character);
  } catch (error) {
    console.error('Error creating character:', error);
    return NextResponse.json(
      { error: 'Failed to create character' },
      { status: 500 }
    );
  }
}
