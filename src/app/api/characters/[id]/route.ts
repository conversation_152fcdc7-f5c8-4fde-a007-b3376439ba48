import { prisma } from '@/lib/prisma';
import { NextResponse } from 'next/server';

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = await params;

    const character = await prisma.character.findUnique({
      where: {
        id
      }
    });

    if (!character) {
      return NextResponse.json(
        { error: 'Character not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(character);
  } catch (error) {
    console.error('Error fetching character:', error);
    return NextResponse.json(
      { error: 'Failed to fetch character' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = await params;
    const data = await request.json();
    const character = await prisma.character.update({
      where: {
        id
      },
      data: {
        name: data.name,
        tags: data.tags,
        creatorNotes: data.creatorNotes,
        description: data.description,
        firstMessage: data.firstMessage,
        altGreetings: data.altGreetings,
        personality: data.personality,
        scenario: data.scenario,
        charNotes: data.charNotes,
        charNotesDepth: data.charNotesDepth,
        dialogueExamples: data.dialogueExamples,
        systemPrompt: data.systemPrompt,
        postInstruction: data.postInstruction,
      }
    });

    return NextResponse.json(character);
  } catch (error) {
    console.error('Error updating character:', error);
    return NextResponse.json(
      { error: 'Failed to update character' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = await params;
    await prisma.character.delete({
      where: {
        id
      }
    });

    return new NextResponse(null, { status: 204 });
  } catch (error) {
    console.error('Error deleting character:', error);
    return NextResponse.json(
      { error: 'Failed to delete character' },
      { status: 500 }
    );
  }
}
