import { NextResponse } from 'next/server';
import { auth } from '@/auth';
import { prisma } from '@/lib/prisma';
import { Message } from '@prisma/client';

export async function GET(
    request: Request,
    { params }: { params: { id: string } }
) {
    try {
        const session = await auth();
        if (!session?.user) {
            return new NextResponse('Unauthorized', { status: 401 });
        }

        const { id } = await params;

        const chat = await prisma.chat.findUnique({
            where: {
                id,
                userId: session.user.id,
            },
            include: {
                messages: {
                    orderBy: {
                        createdAt: 'asc',
                    },
                },
            },
        });

        if (!chat) {
            return new NextResponse('Chat not found', { status: 404 });
        }

        // Transform messages to the expected format
        const messages = chat.messages.map((msg: Message) => {
            const content = msg.content as { role: string; content: string };
            return {
                id: msg.id,
                role: content.role,
                content: content.content,
                createdAt: msg.createdAt,
            };
        });

        return NextResponse.json({
            messages,
            characterId: chat.characterId
        });
    } catch (error) {
        console.error('Error fetching chat:', error);
        return new NextResponse('Internal Server Error', { status: 500 });
    }
} 