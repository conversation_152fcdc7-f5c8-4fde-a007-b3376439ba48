import { NextResponse } from 'next/server';
import { auth } from '@/auth';
import { prisma } from '@/lib/prisma';

export async function POST(
    request: Request,
    { params }: { params: Promise<{ id: string }> }
) {
    try {
        const session = await auth();
        if (!session?.user) {
            return new NextResponse('Unauthorized', { status: 401 });
        }

        const body = await request.json();
        const { role, content } = body;

        // Await params before using its properties
        const { id } = await params;

        const chat = await prisma.chat.findUnique({
            where: {
                id: id,
                userId: session.user.id,
            },
        });

        if (!chat) {
            return new NextResponse('Chat not found', { status: 404 });
        }

        const message = await prisma.message.create({
            data: {
                chatId: id,
                content: {
                    role,
                    content,
                },
            },
        });

        return NextResponse.json(message);
    } catch (error) {
        console.error('Error saving message:', error);
        return new NextResponse('Internal Server Error', { status: 500 });
    }
}