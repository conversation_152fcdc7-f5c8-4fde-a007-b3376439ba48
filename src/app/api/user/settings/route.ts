import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { auth } from '@/auth'

type AllowedLocale = 'en' | 'zh-CN' | 'zh-TW'

export async function GET() {
    const session = await auth();
    if (!session?.user?.id) {
        return new NextResponse(JSON.stringify({ error: "Unauthorized" }), { status: 401 })
    }

    try {
        const settings = await prisma.userSetting.findUnique({
            where: { userId: session.user.id }
        })

        return NextResponse.json({
            webLocale: settings?.webLocale || 'en',
            chatLocale: settings?.chatLocale || 'en'
        })
    } catch (error) {
        return new NextResponse(JSON.stringify({ error: "Failed to load settings" }), { status: 500 })
    }
}

export async function PUT(req: Request) {
    const session = await auth();
    if (!session?.user?.id) {
        return new NextResponse(JSON.stringify({ error: "Unauthorized" }), { status: 401 })
    }

    try {
        const body = await req.json()
        const { webLocale, chatLocale } = body

        // Validate locales - separate validation for webLocale and chatLocale
        const validLocales: AllowedLocale[] = ['en', 'zh-CN', 'zh-TW']

        // WebLocale must be a valid locale
        if (!validLocales.includes(webLocale)) {
            return new NextResponse(JSON.stringify({ message: "Invalid website language value" }), { status: 400 })
        }

        // ChatLocale can be null or a valid locale
        if (chatLocale !== null && !validLocales.includes(chatLocale)) {
            return new NextResponse(JSON.stringify({ message: "Invalid chat language value" }), { status: 400 })
        }

        // Update or create settings
        const updatedSettings = await prisma.userSetting.upsert({
            where: { userId: session.user.id },
            update: {
                webLocale,
                chatLocale
            },
            create: {
                webLocale,
                chatLocale,
                user: {
                    connect: { id: session.user.id }
                }
            }
        })

        return NextResponse.json(updatedSettings)
    } catch (error) {
        console.error("Error saving settings:", error)
        return new NextResponse(JSON.stringify({ message: "Failed to save settings" }), { status: 500 })
    }
} 