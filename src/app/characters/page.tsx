import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { CharacterUpload } from '@/components/CharacterUpload';
import type { CharacterFormData } from '@/components/CharacterForm';
import { getTranslations } from 'next-intl/server';

async function getCharacters(): Promise<CharacterFormData[]> {
  const response = await fetch('http://localhost:3000/api/characters', {
    cache: 'no-store'
  });
  if (!response.ok) throw new Error('Failed to fetch characters');
  return response.json();
}

export default async function CharactersPage({
  params: { locale }
}: {
  params: { locale: string }
}) {
  const t = await getTranslations('characters');
  const characters = await getCharacters();

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">{t('title')}</h1>
        <div className="flex gap-2">
          <CharacterUpload />
          <Link href="/characters/new">
            <Button>{t('createButton')}</Button>
          </Link>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {characters.map((character) => (
          <Link
            key={character.id}
            href={`/characters/${character.id}`}
            className="block"
          >
            <div className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
              <h2 className="text-xl font-bold mb-2">{character.name}</h2>
              {character.creatorNotes && (
                <p className="text-gray-600 mb-4 line-clamp-4">
                  {character.creatorNotes}
                </p>
              )}
              <div className="flex flex-wrap gap-2 min-h-7.5 max-h-15 overflow-hidden">
                {character.tags.map((tag, index) => (
                  <span
                    key={index}
                    className="bg-gray-200 text-gray-800 px-2 py-1 rounded text-sm"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            </div>
          </Link>
        ))}
      </div>

      {characters.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-600 mb-4">{t('noCharacters')}</p>
          <Link href="/characters/new">
            <Button>{t('createFirstButton')}</Button>
          </Link>
        </div>
      )}
    </div>
  );
}
