"use client";

import { CharacterForm } from '@/components/CharacterForm';
import { useTranslations } from 'next-intl';
import { But<PERSON> } from '@/components/ui/button';

export default function NewCharacterPage() {
  const t = useTranslations('characters.new');

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">{t('title')}</h1>
        <Button type="submit" form="character-form">
          {t('createButton')}
        </Button>
      </div>
      <CharacterForm mode="create" />
    </div>
  );
}
