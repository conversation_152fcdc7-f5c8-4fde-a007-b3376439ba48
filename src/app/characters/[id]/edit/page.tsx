import { CharacterForm } from '@/components/CharacterForm';
import type { CharacterFormData } from '@/components/CharacterForm';
import { getTranslations } from 'next-intl/server';
import { Button } from '@/components/ui/button';

async function getCharacter(id: string): Promise<CharacterFormData> {
  const response = await fetch(`http://localhost:3000/api/characters/${id}`);
  if (!response.ok) throw new Error('Failed to fetch character');
  return response.json();
}

export default async function EditCharacterPage({
  params
}: {
  params: { id: string }
}) {
  const t = await getTranslations('characters.edit');
  const character = await getCharacter(params.id);

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">{t('title')}</h1>
      </div>
      <CharacterForm
        mode="edit"
        initialData={character}
        characterId={params.id}
      />
    </div>
  );
}
