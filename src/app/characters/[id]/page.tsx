import { Button } from '@/components/ui/button';
import Link from 'next/link';
import type { CharacterFormData } from '@/components/CharacterForm';
import ReactMarkdown from 'react-markdown';
import { redirect } from 'next/navigation';
import { prisma } from '@/lib/prisma';
import { auth } from '@/auth';
import { getTranslations } from 'next-intl/server';

async function getCharacter(id: string): Promise<CharacterFormData> {
  const response = await fetch(`http://localhost:3000/api/characters/${id}`);
  if (!response.ok) throw new Error('Failed to fetch character');
  return response.json();
}

async function createChat(characterId: string) {
  'use server';

  const session = await auth();
  if (!session?.user?.id) {
    throw new Error('Unauthorized');
  }

  // Check for existing chat
  const existingChat = await prisma.chat.findFirst({
    where: {
      userId: session.user.id,
      characterId,
    },
    orderBy: {
      updatedAt: 'desc'
    }
  });

  if (existingChat) {
    redirect(`/chats/${existingChat.id}`);
  }

  // Create new chat if none exists
  const chat = await prisma.chat.create({
    data: {
      userId: session.user.id,
      characterId,
    }
  });

  redirect(`/chats/${chat.id}`);
}

export default async function CharacterPage({
  params
}: {
  params: { id: string }
}) {
  const { id } = await params;
  const character = await getCharacter(id);
  const t = await getTranslations('characters');

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="grid grid-cols-1 lg:grid-cols-[1fr_2fr] gap-8">
        {/* Left Column */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="aspect-square bg-gray-200 rounded-lg mb-4">
            {/* Add image here if available */}
          </div>
          <div className="flex flex-wrap gap-2 mb-4">
            {character.tags.map((tag, index) => (
              <span
                key={index}
                className="bg-gray-200 text-gray-800 px-2 py-1 rounded text-sm"
              >
                {tag}
              </span>
            ))}
          </div>
          <div className="space-y-2">
            <div className="flex flex-col gap-2">
              <form action={createChat.bind(null, id)}>
                <Button type="submit" className="w-full">
                  {t('chatButton', { name: character.name })}
                </Button>
              </form>
              <Link href={`/characters/${id}/edit`} className="w-full">
                <Button variant="outline" className="w-full">
                  {t('editButton')}
                </Button>
              </Link>
            </div>
          </div>
        </div>

        {/* Right Column */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h1 className="text-3xl font-bold mb-4">{character.name}</h1>

          {character.creatorNotes && (
            <div className="mb-6">
              <h2 className="text-xl font-bold mb-2">{t('sections.creatorNotes')}</h2>
              <div className="prose prose-sm max-w-none">
                <ReactMarkdown>{character.creatorNotes}</ReactMarkdown>
              </div>
            </div>
          )}

          {character.description && (
            <div className="mb-6">
              <h2 className="text-xl font-bold mb-2">{t('sections.description')}</h2>
              <pre className="text-gray-600 whitespace-pre-wrap font-sans">{character.description}</pre>
            </div>
          )}

          {character.firstMessage && (
            <div className="mb-6">
              <h2 className="text-xl font-bold mb-2">First Message</h2>
              <pre className="text-gray-600 whitespace-pre-wrap font-sans">{character.firstMessage}</pre>
            </div>
          )}

          {character.altGreetings.length > 0 && (
            <div className="mb-6">
              <h2 className="text-xl font-bold mb-2">Alternative Greetings</h2>
              <ul className="list-disc pl-5">
                {character.altGreetings.map((greeting, index) => (
                  <li key={index}>
                    <pre className="text-gray-600 whitespace-pre-wrap font-sans inline">{greeting}</pre>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {character.personality && (
            <div className="mb-6">
              <h2 className="text-xl font-bold mb-2">Personality</h2>
              <pre className="text-gray-600 whitespace-pre-wrap font-sans">{character.personality}</pre>
            </div>
          )}

          {character.scenario && (
            <div className="mb-6">
              <h2 className="text-xl font-bold mb-2">Scenario</h2>
              <pre className="text-gray-600 whitespace-pre-wrap font-sans">{character.scenario}</pre>
            </div>
          )}

          {character.charNotes && (
            <div className="mb-6">
              <h2 className="text-xl font-bold mb-2">Character Notes</h2>
              <pre className="text-gray-600 whitespace-pre-wrap font-sans">{character.charNotes}</pre>
            </div>
          )}

          {character.dialogueExamples && (
            <div className="mb-6">
              <h2 className="text-xl font-bold mb-2">Dialogue Examples</h2>
              <pre className="text-gray-600 whitespace-pre-wrap font-sans">{character.dialogueExamples}</pre>
            </div>
          )}

          {character.systemPrompt && (
            <div className="mb-6">
              <h2 className="text-xl font-bold mb-2">Main Prompt</h2>
              <pre className="text-gray-600 whitespace-pre-wrap font-sans">{character.systemPrompt}</pre>
            </div>
          )}

          {character.postInstruction && (
            <div className="mb-6">
              <h2 className="text-xl font-bold mb-2">Post Instruction</h2>
              <pre className="text-gray-600 whitespace-pre-wrap font-sans">{character.postInstruction}</pre>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
