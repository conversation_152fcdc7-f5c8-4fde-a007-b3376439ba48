import { Button } from '@/components/ui/button';
import Link from 'next/link';
import type { CharacterFormData } from '@/components/CharacterForm';
import ReactMarkdown from 'react-markdown';
import { redirect } from 'next/navigation';
import { prisma } from '@/lib/prisma';
import { auth } from '@/auth';
import { getTranslations } from 'next-intl/server';

async function getCharacter(id: string): Promise<CharacterFormData> {
  const response = await fetch(`http://localhost:3000/api/characters/${id}`);
  if (!response.ok) throw new Error('Failed to fetch character');
  return response.json();
}

async function createChat(characterId: string) {
  'use server';

  const session = await auth();
  if (!session?.user?.id) {
    throw new Error('Unauthorized');
  }

  // Check for existing chat
  const existingChat = await prisma.chat.findFirst({
    where: {
      userId: session.user.id,
      characterId,
    },
    orderBy: {
      updatedAt: 'desc'
    }
  });

  if (existingChat) {
    redirect(`/chats/${existingChat.id}`);
  }

  // Create new chat if none exists
  const chat = await prisma.chat.create({
    data: {
      userId: session.user.id,
      characterId,
    }
  });

  redirect(`/chats/${chat.id}`);
}

export default async function CharacterPage({
  params
}: {
  params: { id: string }
}) {
  const { id } = await params;
  const character = await getCharacter(id);
  const t = await getTranslations('characters');

  return (
    <div className="min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50">
      {/* Romantic background elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-10 w-32 h-32 bg-rose-200/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute top-40 right-20 w-48 h-48 bg-pink-200/20 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1s' }}></div>
        <div className="absolute bottom-20 left-1/3 w-40 h-40 bg-purple-200/20 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }}></div>

        {/* Floating hearts */}
        <div className="absolute top-32 left-1/4 text-rose-300/30 text-4xl animate-bounce" style={{ animationDelay: '0.5s' }}>♡</div>
        <div className="absolute top-64 right-1/4 text-pink-300/30 text-3xl animate-bounce" style={{ animationDelay: '1.5s' }}>♡</div>
        <div className="absolute bottom-32 left-1/2 text-purple-300/30 text-5xl animate-bounce" style={{ animationDelay: '2.5s' }}>♡</div>
        <div className="absolute top-1/2 left-16 text-rose-300/20 text-2xl animate-bounce" style={{ animationDelay: '3s' }}>♡</div>
        <div className="absolute top-1/3 right-16 text-pink-300/20 text-6xl animate-bounce" style={{ animationDelay: '0.8s' }}>♡</div>

        {/* Sparkles */}
        <div className="absolute top-24 right-32 text-yellow-300/40 text-xl animate-ping" style={{ animationDelay: '1.2s' }}>✨</div>
        <div className="absolute bottom-40 left-20 text-yellow-300/40 text-lg animate-ping" style={{ animationDelay: '2.8s' }}>✨</div>
        <div className="absolute top-1/2 right-1/3 text-yellow-300/40 text-2xl animate-ping" style={{ animationDelay: '0.3s' }}>✨</div>
      </div>

      <div className="relative container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 lg:grid-cols-[1fr_2fr] gap-12">
          {/* Left Column - Character Portrait & Actions */}
          <div className="bg-white/80 backdrop-blur-sm rounded-3xl shadow-xl border border-rose-100/50 p-8 h-fit">
            {/* Character Portrait */}
            <div className="relative mb-8">
              {/* Decorative border around portrait */}
              <div className="absolute -inset-2 bg-gradient-to-r from-rose-200 via-pink-200 to-purple-200 rounded-3xl blur-sm opacity-50"></div>

              <div className="relative aspect-square bg-gradient-to-br from-rose-100 via-pink-100 to-purple-100 rounded-2xl mb-6 overflow-hidden shadow-2xl border-2 border-white/50">
                {/* Animated decorative heart pattern */}
                <div className="absolute inset-0 opacity-15">
                  <div className="absolute top-4 left-4 text-rose-300 text-2xl animate-pulse">♡</div>
                  <div className="absolute top-8 right-6 text-pink-300 text-lg animate-pulse" style={{ animationDelay: '0.5s' }}>♡</div>
                  <div className="absolute bottom-6 left-8 text-rose-300 text-xl animate-pulse" style={{ animationDelay: '1s' }}>♡</div>
                  <div className="absolute bottom-4 right-4 text-pink-300 text-sm animate-pulse" style={{ animationDelay: '1.5s' }}>♡</div>
                  <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-purple-300 text-3xl animate-pulse" style={{ animationDelay: '2s' }}>♡</div>
                </div>

                {/* Central heart with glow effect */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="relative">
                    <div className="absolute inset-0 text-rose-400/30 text-8xl font-light blur-lg animate-pulse">♡</div>
                    <div className="relative text-rose-400/60 text-6xl font-light animate-bounce" style={{ animationDelay: '0.3s' }}>♡</div>
                  </div>
                </div>

                {/* Shimmer effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 animate-pulse"></div>
              </div>

              {/* Character Name */}
              <h1 className="text-3xl font-bold text-center bg-gradient-to-r from-rose-600 via-pink-600 to-purple-600 bg-clip-text text-transparent mb-4">
                {character.name}
              </h1>
            </div>

            {/* Tags */}
            <div className="flex flex-wrap gap-2 mb-8 justify-center">
              {character.tags.map((tag, index) => (
                <span
                  key={index}
                  className="bg-gradient-to-r from-rose-100 to-pink-100 text-rose-700 px-4 py-2 rounded-full text-sm font-medium border border-rose-200/50 shadow-sm"
                >
                  {tag}
                </span>
              ))}
            </div>

            {/* Action Buttons */}
            <div className="space-y-4">
              <form action={createChat.bind(null, id)}>
                <Button
                  type="submit"
                  className="w-full bg-gradient-to-r from-rose-500 via-pink-500 to-purple-500 hover:from-rose-600 hover:via-pink-600 hover:to-purple-600 text-white font-medium py-3 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
                >
                  💕 {t('chatButton', { name: character.name })}
                </Button>
              </form>
              <Link href={`/characters/${id}/edit`} className="w-full">
                <Button
                  variant="outline"
                  className="w-full border-2 border-rose-300 text-rose-600 hover:bg-rose-50 hover:border-rose-400 font-medium py-3 rounded-2xl transition-all duration-300 hover:shadow-md"
                >
                  ✨ {t('editButton')}
                </Button>
              </Link>
            </div>
          </div>

          {/* Right Column - Character Details */}
          <div className="bg-white/80 backdrop-blur-sm rounded-3xl shadow-xl border border-rose-100/50 p-8">
            {/* Character Details Header */}
            <div className="mb-8 text-center">
              <div className="inline-flex items-center gap-2 text-rose-600 mb-4">
                <span className="text-2xl">✨</span>
                <h2 className="text-2xl font-bold bg-gradient-to-r from-rose-600 to-pink-600 bg-clip-text text-transparent">
                  Character Details
                </h2>
                <span className="text-2xl">✨</span>
              </div>
            </div>

            <div className="space-y-8">

              {character.creatorNotes && (
                <div className="bg-gradient-to-r from-rose-50 to-pink-50 rounded-2xl p-6 border border-rose-100/50">
                  <h3 className="text-lg font-semibold text-rose-700 mb-4 flex items-center gap-2">
                    <span className="text-rose-500">💝</span>
                    {t('sections.creatorNotes')}
                  </h3>
                  <div className="prose prose-sm max-w-none text-rose-800">
                    <ReactMarkdown>{character.creatorNotes}</ReactMarkdown>
                  </div>
                </div>
              )}

              {character.description && (
                <div className="bg-gradient-to-r from-pink-50 to-purple-50 rounded-2xl p-6 border border-pink-100/50">
                  <h3 className="text-lg font-semibold text-pink-700 mb-4 flex items-center gap-2">
                    <span className="text-pink-500">🌸</span>
                    {t('sections.description')}
                  </h3>
                  <div className="text-pink-800 whitespace-pre-wrap font-sans leading-relaxed">
                    {character.description}
                  </div>
                </div>
              )}

              {character.firstMessage && (
                <div className="bg-gradient-to-r from-purple-50 to-rose-50 rounded-2xl p-6 border border-purple-100/50">
                  <h3 className="text-lg font-semibold text-purple-700 mb-4 flex items-center gap-2">
                    <span className="text-purple-500">💌</span>
                    First Message
                  </h3>
                  <div className="text-purple-800 whitespace-pre-wrap font-sans leading-relaxed italic bg-white/50 p-4 rounded-xl border border-purple-100">
                    "{character.firstMessage}"
                  </div>
                </div>
              )}

              {character.altGreetings.length > 0 && (
                <div className="bg-gradient-to-r from-rose-50 to-pink-50 rounded-2xl p-6 border border-rose-100/50">
                  <h3 className="text-lg font-semibold text-rose-700 mb-4 flex items-center gap-2">
                    <span className="text-rose-500">💕</span>
                    Alternative Greetings
                  </h3>
                  <div className="space-y-3">
                    {character.altGreetings.map((greeting, index) => (
                      <div key={index} className="text-rose-800 whitespace-pre-wrap font-sans leading-relaxed italic bg-white/50 p-4 rounded-xl border border-rose-100">
                        "{greeting}"
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {character.personality && (
                <div className="bg-gradient-to-r from-pink-50 to-purple-50 rounded-2xl p-6 border border-pink-100/50">
                  <h3 className="text-lg font-semibold text-pink-700 mb-4 flex items-center gap-2">
                    <span className="text-pink-500">🦋</span>
                    Personality
                  </h3>
                  <div className="text-pink-800 whitespace-pre-wrap font-sans leading-relaxed">
                    {character.personality}
                  </div>
                </div>
              )}

              {character.scenario && (
                <div className="bg-gradient-to-r from-purple-50 to-rose-50 rounded-2xl p-6 border border-purple-100/50">
                  <h3 className="text-lg font-semibold text-purple-700 mb-4 flex items-center gap-2">
                    <span className="text-purple-500">🌹</span>
                    Scenario
                  </h3>
                  <div className="text-purple-800 whitespace-pre-wrap font-sans leading-relaxed">
                    {character.scenario}
                  </div>
                </div>
              )}

              {character.charNotes && (
                <div className="bg-gradient-to-r from-rose-50 to-pink-50 rounded-2xl p-6 border border-rose-100/50">
                  <h3 className="text-lg font-semibold text-rose-700 mb-4 flex items-center gap-2">
                    <span className="text-rose-500">📝</span>
                    Character Notes
                  </h3>
                  <div className="text-rose-800 whitespace-pre-wrap font-sans leading-relaxed">
                    {character.charNotes}
                  </div>
                </div>
              )}

              {character.dialogueExamples && (
                <div className="bg-gradient-to-r from-pink-50 to-purple-50 rounded-2xl p-6 border border-pink-100/50">
                  <h3 className="text-lg font-semibold text-pink-700 mb-4 flex items-center gap-2">
                    <span className="text-pink-500">💬</span>
                    Dialogue Examples
                  </h3>
                  <div className="text-pink-800 whitespace-pre-wrap font-sans leading-relaxed bg-white/50 p-4 rounded-xl border border-pink-100">
                    {character.dialogueExamples}
                  </div>
                </div>
              )}

              {character.systemPrompt && (
                <div className="bg-gradient-to-r from-purple-50 to-rose-50 rounded-2xl p-6 border border-purple-100/50">
                  <h3 className="text-lg font-semibold text-purple-700 mb-4 flex items-center gap-2">
                    <span className="text-purple-500">⚙️</span>
                    Main Prompt
                  </h3>
                  <div className="text-purple-800 whitespace-pre-wrap font-sans leading-relaxed bg-white/50 p-4 rounded-xl border border-purple-100">
                    {character.systemPrompt}
                  </div>
                </div>
              )}

              {character.postInstruction && (
                <div className="bg-gradient-to-r from-rose-50 to-pink-50 rounded-2xl p-6 border border-rose-100/50">
                  <h3 className="text-lg font-semibold text-rose-700 mb-4 flex items-center gap-2">
                    <span className="text-rose-500">📋</span>
                    Post Instruction
                  </h3>
                  <div className="text-rose-800 whitespace-pre-wrap font-sans leading-relaxed bg-white/50 p-4 rounded-xl border border-rose-100">
                    {character.postInstruction}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
