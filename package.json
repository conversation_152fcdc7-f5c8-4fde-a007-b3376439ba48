{"name": "ooaa-web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "vitest"}, "dependencies": {"@auth/prisma-adapter": "^2.10.0", "@prisma/client": "^6.16.1", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-tabs": "^1.1.2", "@tailwindcss/typography": "0.5.16", "@types/bcryptjs": "^2.4.6", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "0.544.0", "next": "^15.5.3", "next-auth": "^5.0.0-beta.29", "next-intl": "^4.3.8", "react": "^19.1.1", "react-dom": "^19.1.1", "react-markdown": "^10.1.0", "tailwind-merge": "3.3.1", "tailwindcss-animate": "^1.0.7", "zod": "^4.1.8"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.13", "@types/jest": "^30.0.0", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "^15.5.3", "postcss": "^8", "prisma": "^6.16.1", "tailwindcss": "^4.1.13", "typescript": "^5", "vitest": "^2.1.8"}}