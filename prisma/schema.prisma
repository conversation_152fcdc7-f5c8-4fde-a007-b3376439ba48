datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}

model User {
  id            String          @id @default(cuid())
  name          String?
  email         String          @unique
  password      String? // Optional since users might sign in with OAuth providers
  emailVerified DateTime?
  image         String?
  accounts      Account[]
  sessions      Session[]
  // Optional for WebAuthn support
  Authenticator Authenticator[]

  characters       Character[]
  chats            Chat[]
  favoriteChars    FavoritesOnCharacters[]
  personas         Persona[]
  defaultPersona   Persona?                @relation("DefaultPersona", fields: [defaultPersonaId], references: [id])
  defaultPersonaId String?                 @unique
  userSettingId    String?                 @unique
  userSetting      UserSetting?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([email])
}

model Account {
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([provider, providerAccountId])
}

model Session {
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model VerificationToken {
  identifier String
  token      String
  expires    DateTime

  @@id([identifier, token])
}

// Optional for WebAuthn support
model Authenticator {
  credentialID         String  @unique
  userId               String
  providerAccountId    String
  credentialPublicKey  String
  counter              Int
  credentialDeviceType String
  credentialBackedUp   Boolean
  transports           String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([userId, credentialID])
}

model Character {
  id        String   @id @default(cuid())
  name      String
  tags      String[]
  likeCount Int      @default(0)

  creatorNotes String?
  description  String?
  firstMessage String?
  altGreetings String[]

  personality      String?
  scenario         String?
  charNotes        String?
  charNotesDepth   Int?    @default(0)
  dialogueExamples String?

  systemPrompt    String?
  postInstruction String?

  createdById String?
  createdBy   User?     @relation(fields: [createdById], references: [id], onDelete: SetNull, onUpdate: Cascade)
  createdAt   DateTime  @default(now())
  deletedAt   DateTime?
  versionId   String    @default(cuid())

  chats      Chat[]
  favoriteBy FavoritesOnCharacters[]

  @@index([createdAt])
  @@index([createdById, createdAt])
  @@index([createdById, likeCount])
  @@index([likeCount])
}

model Chat {
  id String @id @default(cuid())

  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  characterId String?
  character   Character? @relation(fields: [characterId], references: [id], onDelete: SetNull, onUpdate: Cascade)

  messages Message[]

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  @@index([userId, createdAt])
  @@index([characterId, createdAt])
}

model Message {
  id        String   @id @default(cuid())
  chatId    String
  chat      Chat     @relation(fields: [chatId], references: [id], onDelete: Cascade)
  content   Json
  createdAt DateTime @default(now())
}

model FavoritesOnCharacters {
  characterId String
  character   Character @relation(fields: [characterId], references: [id], onDelete: Cascade)
  userId      String
  user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  createdAt   DateTime  @default(now())

  @@id([characterId, userId])
}

model Persona {
  id          String   @id @default(cuid())
  name        String
  description String?
  createdAt   DateTime @default(now())

  userId     String
  user       User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  defaultFor User?  @relation("DefaultPersona")

  @@index([userId, createdAt])
}

model UserSetting {
  id         String  @id @default(cuid())
  webLocale  String? // For website UI localization
  chatLocale String? // For LLM response localization
  userId     String  @unique
  user       User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}
