/*
  Warnings:

  - A unique constraint covering the columns `[defaultPersonaId]` on the table `User` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterTable
ALTER TABLE "User" ADD COLUMN     "defaultPersonaId" TEXT;

-- CreateTable
CREATE TABLE "Persona" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "userId" TEXT NOT NULL,

    CONSTRAINT "Persona_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "Persona_userId_createdAt_idx" ON "Persona"("userId", "createdAt");

-- CreateIndex
CREATE UNIQUE INDEX "User_defaultPersonaId_key" ON "User"("defaultPersonaId");

-- AddForeignKey
ALTER TABLE "User" ADD CONSTRAINT "User_defaultPersonaId_fkey" FOREIGN KEY ("defaultPersonaId") REFERENCES "Persona"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Persona" ADD CONSTRAINT "Persona_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
